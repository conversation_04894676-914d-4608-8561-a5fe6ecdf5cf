const production = process.env.NODE_ENV === 'production'
module.exports = {
  apps: [
    {
      name: 'tqq-2024-mp-api',
      script: './server.js',
      watch: !production,
      ignore_watch: ['node_modules', '*.log', '.txt', 'logs'],
      watch_options: { followSymlinks: false },
      exec_mode: production ? 'cluster' : 'fork',
      instances: production ? 2 : 1,
      wait_ready: false,
      instance_var: 'tqq-2024-mp-api',
      merge_logs: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss'
    }
  ],
  deploy: {
    production: {
      host: ['aliyun'],
      ref: 'origin/main',
      repo: '**************:ynf/tqq-mp-api-2024.git',
      path: '/root/sites/tqq2024/mp-api',
      ssh_options: 'StrictHostKeyChecking=no',
      // 'post-setup': 'export NODE_ENV=production && ls -la && npm install --omit=dev',
      'pre-deploy-local': 'export NODE_ENV=production',
      'pre-deploy': "echo '开始拉取'",
      'post-deploy': 'export NODE_ENV=production && pm2 reload pm2.config.cjs'
    },
    dev: {
      host: ['aleiyun'],
      ref: 'origin/main',
      repo: '**************:ynf/tqq-mp-api-2024.git',
      path: '/root/sites/tqq-dev/mp-api',
      ssh_options: 'StrictHostKeyChecking=no',
      // 'post-setup': 'export NODE_ENV=production && ls -la && npm install --omit=dev',
      'pre-deploy-local': 'export NODE_ENV=production',
      'pre-deploy': "echo '开始拉取'",
      'post-deploy': 'export NODE_ENV=production && pm2 reload pm2.config.cjs'
    }
  }
}
