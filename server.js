import fs from 'node:fs'
import { createServer } from 'node:http'
import { ApolloServer } from '@apollo/server'
import { expressMiddleware } from '@apollo/server/express4'
import { ApolloServerPluginDrainHttpServer } from '@apollo/server/plugin/drainHttpServer'
import config from 'config'
import express from 'express'
import { makeExecutableSchema } from '@graphql-tools/schema'
import { mapSchema, getDirective, MapperKind } from '@graphql-tools/utils'
import { GraphQLError, defaultFieldResolver } from 'graphql'

import httpErrorCode from './util/http-error-code.js'
import resolvers from './graphql/resolvers/index.js'
import kn from './db/kn.js'
import loaders from './db/loaders.js'
import { createAccount, getAccountByToken, getAccountTokenById, reSaveAccount } from './helper/account.js'
import MPApi from './helper/mp-api.js'
import wechatPayRoute from './routes/wechat-pay.js'
import qiniuRoute from './routes/qiniu.js'
import wechatRoute from './routes/wechat.js'
// import imagesRoute from './routes/images.js'
import { hashID } from './util/hashids.js'

const app = express()
const httpServer = createServer(app)
const mps = config.get('mp')
const typeDefs = fs.readFileSync(new URL('./graphql/schema.graphql', import.meta.url), 'utf-8')
let schema = makeExecutableSchema({ typeDefs: [typeDefs], resolvers })
// Transform the schema by applying directive logic
const directives = [
  // auth
  (fieldConfig) => {
    const authDirective = getDirective(schema, fieldConfig, 'auth')?.[0]
    if (authDirective) {
      const { resolve = defaultFieldResolver } = fieldConfig
      fieldConfig.resolve = async function (source, args, context, info) {
        if (!context.account) throw new GraphQLError('没有登录', { extensions: { code: httpErrorCode.UNAUTHORIZED } })
        if (authDirective.profiled && !context.account.name) throw new GraphQLError('请完善个人信息', { extensions: { code: httpErrorCode.FORBIDDEN } })
        // if (context.account.status !== 1) throw new GraphQLError('账号已被禁用', { extensions: { code: httpErrorCode.FORBIDDEN } })
        return resolve(source, args, context, info)
      }
      return fieldConfig
    }
  },
  // refreshCache
  (fieldConfig) => {
    const refreshCacheDirective = getDirective(schema, fieldConfig, 'refreshCache')?.[0]
    if (refreshCacheDirective) {
      const { resolve = defaultFieldResolver } = fieldConfig
      fieldConfig.resolve = async function (source, args, context, info) {
        const result = await resolve(source, args, context, info)
        if (refreshCacheDirective.returnAccount) {
          const user = await reSaveAccount(context.account.id)
          return user
        } else {
          return result
        }
      }
      return fieldConfig
    }
  },
  // check query profile id
  (fieldConfig) => {
    const authDirective = getDirective(schema, fieldConfig, 'checkProfileId')?.[0]
    if (authDirective) {
      const { resolve = defaultFieldResolver } = fieldConfig
      fieldConfig.resolve = async function (source, args, context, info) {
        if (context.account) {
          let profileId = args.profileId
          if (!profileId) {
            profileId = context.account.profiles[0].id
          } else if (!context.account.profiles.find(i => i.id === profileId)) {
            throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
          }
          return resolve(source, { ...args, profileId }, context, info)
        } else {
          throw new GraphQLError('没有登录', { extensions: { code: httpErrorCode.UNAUTHORIZED } })
        }
      }
      return fieldConfig
    }
  }
]
schema = directives.reduce((schema, directive) => mapSchema(schema, { [MapperKind.OBJECT_FIELD]: directive }), schema)

// 创建记录处理时间的插件
const requestTimingPlugin = {
  async requestDidStart (initialRequestContext) {
    return {
      async executionDidStart (executionRequestContext) {
        return {
          willResolveField ({ source, args, contextValue, info }) {
            const start = Date.now()
            return (error, result) => {
              const end = Date.now()
              const took = end - start
              if (took > 100) {
                console.log(`[${info.parentType.name}.${info.fieldName}] [${took}ms] ${error ? '[error]' : ''}`)
              }
            }
          }
        }
      },
      async didEncounterErrors (ctx) {
        const { errors, request } = ctx
        // const hasAuthError =
        // 检查 errors 中是否所有对象的 extensions.code 都为 'UNAUTHORIZED'
        const isAllAuthError = errors.every(err => err?.extensions?.code === httpErrorCode.UNAUTHORIZED)
        if (!isAllAuthError) {
          // 打印错误信息
          console.error(errors)
          // 打印请求参数（变量）
          console.error('Variables:', request.variables)
          // 打印查询
          console.error('Query:', request.query)
        }
        // else {
        //   console.log(errors.map(err => err.path || '').join('|'))
        // }
        // // 打印 operationName
        // console.error('Operation Name:', request.operationName)
      }
    }
  }
}

const server = new ApolloServer({
  schema,
  csrfPrevention: true,
  plugins: [ApolloServerPluginDrainHttpServer({ httpServer }), requestTimingPlugin],
  status400ForVariableCoercionErrors: true,
  includeStacktraceInErrorResponses: false, // process.env.NODE_ENV !== 'production',
  formatError (formattedError, err) {
    if (!formattedError.extensions) return { message: '服务器出错了', code: httpErrorCode.INTERNAL_SERVER_ERROR }
    if (formattedError.extensions.code === httpErrorCode.INTERNAL_SERVER_ERROR) return { message: '服务器出错了', code: httpErrorCode.INTERNAL_SERVER_ERROR }
    return { message: formattedError.message, code: err.extensions.code }
  }
})
await server.start()

app.disable('x-powered-by')
// app.get('/nWXwONBXrF.txt', function (req, res) { res.send('654e81c18b330b9261ff05cd2e93fd73') })
// app.use(morgan(':remote-addr UserId(:userId) ":method :url" :body :status :response-time ms - :res[content-length]'))
app.use(express.json({ limit: '10mb' }))
// app.use(urlencoded({ extended: true, limit: '2mb' }))
app.use(express.text({ limit: '2mb' }))
app.use('/graphql', expressMiddleware(server, {
  async context ({ req }) {
    let account = null
    let token = req.get('App-Token') ?? ''
    const app = req.get('App') ?? ''
    if (!mps[app]) {
      throw new GraphQLError('没有相应的程序', { extensions: { code: httpErrorCode.BAD_REQUEST } })
    }
    if (token) {
      account = await getAccountByToken(token)
    } else if (req.get('Login-Code')) {
      // 使用 wx.login 返回的code，判断是否注册过，若没有创建最基本用户
      const mp = new MPApi(app)
      const { unionId, openId } = await mp.code2session(req.get('Login-Code'))
      const u = await kn('user').where({ app, openId }).first(['id', 'openId', 'mobile'])
      if (u) {
        ({ user: account, token } = await getAccountTokenById(u.id))
      } else {
        let referrerUserId = 0
        const referrerUserIdEncoded = req.get('Referrer-User-Id')
        if (referrerUserIdEncoded) {
          try {
            referrerUserId = hashID.decode(referrerUserIdEncoded)[0] ?? 0
          } catch (err) {
            console.error(err)
          }
          const referrerUser = await kn('user').where({ id: referrerUserId }).first('id')
          if (!referrerUser) referrerUserId = 0
        }
        ({ user: account, token } = await createAccount({ app, openId, unionId, referrerUserId }))
      }
    }
    return { loaders: loaders(account), app, token, account }
  }
}))
app.use('/wechat-pay', wechatPayRoute)
app.use('/qiniu', qiniuRoute)
app.use('/wechat', wechatRoute)
// app.use('/images', imagesRoute)
httpServer.on('error', error => console.error(error))
await new Promise(resolve => httpServer.listen({ port: config.get('port') }, resolve))
console.log(`🚀 Server ready at port ${config.get('port')} graphql`)
