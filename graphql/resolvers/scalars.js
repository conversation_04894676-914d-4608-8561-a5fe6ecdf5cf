import { GraphQLScalarType } from 'graphql'
import { hashID } from '../../util/hashids.js'
import dayjs from 'dayjs'

const ID = new GraphQLScalarType({
  name: 'ID',
  serialize: (v) => v ? hashID.encode(v) : '',
  parseValue: (v) => {
    if (v) {
      const d = hashID.decode(v)
      if (d && d.length) return d[0]
    }
    return null
  }
})

const DateTime = new GraphQLScalarType({
  name: 'DateTime',
  serialize: (v) => !v ? null : typeof v === 'string' ? dayjs(v).valueOf() : v.valueOf(),
  parseValue: (v) => v ? new Date(v) : null
})
const Birth = new GraphQLScalarType({
  name: 'Birth',
  serialize: (v) => {
    if (v) return dayjs(v).format('YYYY-MM-DD')
    return ''
  },
  parseValue: (v) => v
})

const QueryDate = new GraphQLScalarType({
  name: 'QueryDate',
  parseValue: (v) => {
    if (v && typeof v && /^(1\d{3}|20\d{2})-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/.test(v)) {
      return v
    }
    return null
  }
})

export default {
  ID,
  DateTime,
  Birth,
  QueryDate
}
