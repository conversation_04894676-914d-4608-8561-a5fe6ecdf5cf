import { GraphQLError } from 'graphql'
import _ from 'lodash-es'
import { round } from 'mathjs'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween.js'
import objectSupport from 'dayjs/plugin/objectSupport.js'
import { Readable } from 'stream'
import config from 'config'

import kn from '../../db/kn.js'
import redis from '../../db/redis.js'
import httpErrorCode from '../../util/http-error-code.js'
import { getUploadToken, uploadImage, checkFileExists } from '../../helper/qiniu.js'
import { hashID, orderHashID } from '../../util/hashids.js'
import { createStatus, removeStatus } from '../../helper/status.js'
import MPApi from '../../helper/mp-api.js'
import targetWeight from '../../helper/target-weight.js'
import { createPrepayOrder, getPrepayInfo, requestRefund } from '../../helper/wechat-pay.js'
import { sendText } from '../../helper/work-wechat.js'
import { sports, pressures, measurements, glucoses, lipids, acids } from '../../helper/status-data.js'

const { refundNotifyUrl, orderPrefix } = config.get('wechat-pay')

const imageHost = config.get('imgHost')
dayjs.extend(objectSupport)
dayjs.extend(isBetween)

export default {
  Profile: {
    age: (parent) => {
      if (!parent.birth) return ''
      const y = dayjs().diff(parent.birth, 'year', true)
      const a = Math.floor(y)
      return a + '岁'
    },
    genderLabel: ({ gender }) => gender === 1 ? '男' : gender === 2 ? '女' : '未知',
    lastWeight: async (parent, args, { loaders, account }) => {
      if (!parent.weight) return null
      const lastWeight = await kn('day_weight')
        .where({ userId: account.id, profileId: parent.id })
        .where('date', '<', dayjs(parent.weightAt).format('YYYY-MM-DD'))
        .orderBy('date', 'desc').first()
      if (!lastWeight) return null
      return { weight: lastWeight.weight, date: lastWeight.date, changes: round(parent.weight - lastWeight.weight, 2) }
    }
  },
  User: {
    name: (parent) => !parent.name ? '用户' + hashID.encode(parent.id) : parent.name,
    followed: async (parent, arg, { loaders }) => loaders.userFollowedLoader.load(parent.id)
  },
  Status: {
    user: (parent, args, { loaders }) => loaders.userLoader.load(parent.userId),
    profile: (parent, args, { loaders }) => loaders.profileLoader.load(parent.profileId),
    content: (parent) => parent.illegal ? '内容涉嫌违规' : parent.deleted ? '已删除' : parent.content,
    images: (parent, args, { loaders }) => parent.illegal || parent.deleted ? [] : loaders.statusImagesLoader.load(parent.id),
    weight: async ({ id, deleted, illegal, type }, arg, { loaders, account }) => deleted || illegal || type !== 'weight' ? null : loaders.statusWeightLoader.load(id),
    sport: async ({ id, deleted, illegal, type }, arg, { loaders }) => deleted || illegal || type !== 'sport' ? null : loaders.statusSportLoader.load(id),
    meal: async ({ id, deleted, illegal, type }, arg, { loaders }) => deleted || illegal || type !== 'meal' ? null : loaders.statusMealLoader.load(id),
    measurement: async ({ id, deleted, illegal, type }, arg, { loaders }) => deleted || illegal || type !== 'measurement' ? null : loaders.statusMeasurementLoader.load(id),
    pressure: async ({ id, deleted, illegal, type }, arg, { loaders }) => deleted || illegal || type !== 'pressure' ? null : loaders.statusPressureLoader.load(id),
    glucose: async ({ id, deleted, illegal, type }, arg, { loaders }) => deleted || illegal || type !== 'glucose' ? null : loaders.statusGlucoseLoader.load(id),
    lipid: async ({ id, deleted, illegal, type }, arg, { loaders }) => deleted || illegal || type !== 'lipid' ? null : loaders.statusLipidLoader.load(id),
    acid: async ({ id, deleted, illegal, type }, arg, { loaders }) => deleted || illegal || type !== 'acid' ? null : loaders.statusAcidLoader.load(id),
    liked: async (parent, arg, { loaders }) => parent.deleted ? false : loaders.statusLikeLoader.load(parent.id)
  },
  Comment: {
    user: async (parent, arg, { loaders }) => loaders.userLoader.load(parent.userId),
    status: async (parent, arg, { loaders }) => loaders.statusLoader.load(parent.statusId),
    parent: async (parent, arg, { loaders }) => parent.parentId ? loaders.commentLoader.load(parent.parentId) : null
  },
  Like: {
    status: async ({ entityType, entityId }, arg, { loaders }) => entityType === 'status' ? loaders.statusLoader.load(entityId) : null,
    comment: async ({ entityType, entityId }, arg, { loaders }) => entityType === 'comment' ? loaders.commentLoader.load(entityId) : null
  },
  Notification: {
    sender: async (parent, arg, { loaders }) => parent.senderId ? loaders.userLoader.load(parent.senderId) : null,
    like: async (parent, arg, { loaders }) => parent.category === 'like' ? loaders.likeLoader.load(parent.likeId) : null,
    comment: async (parent, arg, { loaders }) => parent.category === 'comment' ? loaders.commentLoader.load(parent.commentId) : null,
    replyComment: async (parent, arg, { loaders }) => parent.category === 'comment' && parent.replyCommentId ? loaders.commentLoader.load(parent.replyCommentId) : null
  },
  Order: {
    products: async ({ id }, arg, { loaders }) => loaders.orderProductsLoader.load(id),
    shippings: async ({ id }, arg, { loaders }) => loaders.orderShippingLoader.load(id)
  },
  OrderCommission: {
    user: async ({ userId }, arg, { loaders }) => loaders.userLoader.load(userId)
  },
  Plan: {
    todayIndex: ({ status, startDate }) => status !== 1 ? -1 : dayjs({ hour: 0 }).diff(dayjs(startDate), 'day') + 1,
    shenRewardUsers: async ({ userId, status, isShenPaybackPlan }, arg, { loaders }) => {
      if (status !== 1 || !isShenPaybackPlan) return []
      const user = await loaders.userLoader.load(userId)
      const userIds = user.referrerUserId ? [user.referrerUserId, 10684, 10659] : [10684, 10659]
      return loaders.userLoader.loadMany(userIds)
    },
    shenPaybackEverydayAmount: async ({ id, orderId, status, isShenPaybackPlan, dates }) => {
      if (!isShenPaybackPlan) return 0
      const order = !orderId ? null : await kn('order').where({ id: orderId }).first()
      return order ? Math.floor(order.finalAmount / dates) : 0
    },
    todayWeight: async ({ id, orderId, status, isShenPaybackPlan, startDate, endDate, userId, profileId }) => {
      if (status !== 1) return null
      const today = dayjs({ hour: 0 })
      if (today.isBetween(dayjs(startDate), dayjs(endDate), 'day', '[]')) {
        const info = await kn('plan_target').where({ planId: id, date: today.format('YYYY-MM-DD') }).first()
        const weight = await kn('day_weight').where({ userId, profileId, date: today.format('YYYY-MM-DD') }).first()
        return weight ? { ...info, ..._.pick(weight, 'weight', 'bmi', 'bf') } : info
      } else {
        return { dateIndex: 0 }
      }
    },
    weights: async ({ id, startDate, endDate, userId, profileId }) => {
      const weights = await kn('day_weight').where({ userId, profileId })
        .where('date', '>=', startDate).where('date', '<=', endDate).orderBy('date', 'asc')
      const targets = await kn('plan_target').where({ planId: id }).orderBy('date', 'asc')
      let j = 0
      const results = targets.map((i, index) => {
        const d = dayjs(i.date).valueOf()
        let weightInfo = null
        for (; j < weights.length; j++) {
          const dv = dayjs(weights[j].date).valueOf()
          if (dv > d) {
            break
          } if (dv === d) {
            weightInfo = weights[j]
            j++
            break
          }
        }
        // const m = { date: i.date,  dateIndex: i.dateIndex, targetWeight: i.targetWeight, targetLose: i.targetLose }
        return weightInfo ? { ...i, ..._.pick(weightInfo, 'weight', 'bmi', 'bf', 'previous') } : i
      })
      return results
    }
  },
  PlanDateWeight: {
    refundInfo: async ({ refundId }, arg, { loaders }) => refundId ? loaders.orderRefundLoader.load(refundId) : null,
    commissionInfo: async ({ commissionId }, arg, { loaders }) => commissionId ? loaders.orderCommissionLoader.load(commissionId) : null
  },
  Course: {
    sections: async ({ id }, arg, { loaders }) => loaders.courseSectionsLoader.load(id)
  },
  MyCourse: {
    course: async ({ courseId }, arg, { loaders }) => loaders.courseLoader.load(courseId)
  },
  Query: {
    account: (parent, { loginCode }, { account, app, token }) => {
      if (account) {
        kn('user').where({ id: account.id }).update({ lastLoginAt: kn.raw('CURRENT_TIMESTAMP') }).then(() => { })
        return { user: account, token }
      }
      return null
    },
    weights: async (parent, { profileId, dates, start, end }, { account, loaders }, info) => {
      const requestPlan = info.fieldNodes[0].selectionSet.selections.some(selection => selection.name.value === 'targetWeight')
      let plan = null
      if (requestPlan) {
        plan = await loaders.progressingPlanLoader.load(profileId)
      }
      if (plan && plan.status === 1) {
        const { startDate, endDate } = plan
        const weights = await kn('day_weight').where({ userId: account.id, profileId })
          .where('date', '>=', dayjs(startDate).format('YYYY-MM-DD')).where('date', '<=', dayjs(endDate).format('YYYY-MM-DD')).orderBy('date', 'asc')
        const targets = await kn('plan_target').where({ planId: plan.id }).orderBy('date', 'asc')
        let j = 0
        const results = targets.map((i, index) => {
          const d = dayjs(i.date).valueOf()
          let weightInfo = null
          for (; j < weights.length; j++) {
            const dv = dayjs(weights[j].date).valueOf()
            if (dv > d) {
              break
            } if (dv === d) {
              weightInfo = weights[j]
              j++
              break
            }
          }
          const m = { date: i.date, targetWeight: i.targetWeight }
          return weightInfo ? { ...m, ...weightInfo } : m
          // return m
        })
        return results
      } else {
        const qb = kn('day_weight').where({ userId: account.id, profileId }).orderBy('date', 'desc')
        if (dates && dates <= 1000) {
          qb.where('date', '>=', dayjs().subtract(dates, 'days').format('YYYY-MM-DD'))
        } else if (start && end) {
          if (start) qb.where('date', '>=', start)
          if (end) qb.where('date', '<', dayjs(end).add(1, 'day').format('YYYY-MM-DD'))
        } else {
          qb.limit(100)
        }
        const weights = await qb.select()
        return weights.reverse()
      }
    },
    weightsWeek: async (parent, { profileId }, { account }) => {
      if (!profileId) profileId = account.profiles[0].id
      const today = dayjs({ hour: 0 })
      const startDate = today.day() === 0 ? today.startOf('week').subtract(6, 'days') : today.startOf('week').add(1, 'day')
      const weights = await kn('day_weight').where({ userId: account.id, profileId })
        .where('date', '>=', startDate.format('YYYY-MM-DD'))
        .orderBy('date', 'asc')
      const results = []
      let j = 0
      for (let i = 0; i < 7; i++) {
        const date = startDate.add(i, 'day').valueOf()
        let weight = 0
        for (; j < weights.length; j++) {
          const dv = weights[j].date.valueOf()
          if (dv > date) {
            break
          } else if (dv === date) {
            weight = weights[j].weight
            j++
            break
          }
        }
        results.push({ date, weight })
      }
      return results
    },
    weightsMonth: async (parent, { profileId }, { account }) => {
      if (!profileId) profileId = account.profiles[0].id
      const today = dayjs({ hour: 0 })
      const lastDate = today.clone().endOf('month').date()
      const day1 = today.startOf('month')
      const [{ count }] = await kn('day_weight').where({ userId: account.id, profileId })
        .where('date', '>=', day1.format('YYYY-MM-DD'))
        .count('id as count')
      const result = { dates: lastDate, weightedDates: count, today: today.date() }
      return result
    },
    sports: async (parent, { profileId, dates, start, end }, { account, loaders }, info) => sports(account.id, profileId, dates, start, end),
    measurements: async (parent, { profileId, dates, start, end }, { account, loaders }, info) => measurements(account.id, profileId, dates, start, end),
    pressures: (parent, { profileId, dates, start, end }, { account, loaders }) => pressures(account.id, profileId, dates, start, end),
    glucoses: (parent, { profileId, dates, start, end }, { account, loaders }) => glucoses(account.id, profileId, dates, start, end),
    lipids: (parent, { profileId, dates, start, end }, { account, loaders }) => lipids(account.id, profileId, dates, start, end),
    acids: (parent, { profileId, dates, start, end }, { account, loaders }) => acids(account.id, profileId, dates, start, end),
    status: async (parent, { userId, month, profileId, lastId, type, start, end }, { account }) => {
      const qb = kn('status').where({ deleted: false }).orderBy('id', 'desc')
      if (userId) {
        qb.where({ userId, isPublic: true, illegal: 0 })
      } else {
        qb.where({ userId: account.id })
        if (profileId) qb.where({ profileId })
        if (month) {
          if (!/^(\d{4})-(\d{2})$/.test(month)) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
          const startMonth = month + '-01'
          const endMonth = dayjs(start).endOf('month').format('YYYY-MM-DD')
          qb.where({ userId: account.id }).whereBetween('createAt', [startMonth, endMonth])
        }
        if (type) qb.where({ type })
        if (start) qb.where('createAt', '>=', start)
        if (end) qb.where('createAt', '<', dayjs(end).add(1, 'day').format('YYYY-MM-DD'))
      }
      if (!month) {
        qb.limit(20)
        if (lastId) qb.where('id', '<', lastId)
      }
      return qb.select()
    },
    singleStatus: async (parent, { id }, { account }) => {
      const item = await kn('status').where({ id }).first()
      if (!item || item.deleted || item.illegal) throw new GraphQLError('内容不存在或已删除', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (!item.isPublic && (!account || item.userId !== account.id)) throw new GraphQLError('没有查看权限', { extensions: { code: httpErrorCode.FORBIDDEN } })
      return item
    },
    recommendedStatus: async (parent, { lastId }, { account, app }) => {
      const qb = kn('status')
        .where({ app, deleted: false, isPublic: true, illegal: 0 })
        .where(q => {
          q.where('content', '!=', '').orWhereIn('id', kn('status_image').select('statusId'))
        })
        .orderBy('id', 'desc')
      if (lastId) qb.where('id', '<', lastId)
      return qb.limit(20)
    },
    comments: async (__, { statusId, lastId }, { loaders }) => {
      // 没有 parentId 的评论，即为一级评论
      const temps1 = await kn('comment').where(qb => {
        qb.where({ statusId, parentId: 0 })
        if (lastId) qb.where('id', '<', lastId)
      }).orderBy('id', 'desc').limit(20).select(['id', 'parentId'])
      const ids1 = temps1.map(i => i.id)
      const list = await loaders.commentLoader.loadMany(ids1)
      // 二级评论
      const temps2 = ids1.length === 0 ? [] : await kn('comment_parent').whereIn('parentId', list.map(i => i.id)).select()
      const ids2 = temps2.map(i => i.commentId)
      const relations = _.reduce(temps2, (memo, i) => ({ ...memo, [i.commentId]: i.parentId }), {})
      let replies = await loaders.commentLoader.loadMany(ids2)
      replies = replies.map(i => ({ ...i, keyId: relations[i.id] }))
      const repliesGrouped = _.groupBy(replies, 'keyId')
      return list.map(i => ({
        ...i,
        replies: repliesGrouped[i.id] || []
      }))
    },
    user: async (parent, { id }) => {
      const user = await kn('user').where({ id }).first()
      if (!user) throw new GraphQLError('用户不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      return user
    },
    myLikes: async (parent, { lastId }, { account }) => {
      const qb = kn('like').where({ userId: account.id, deleted: false })
      if (lastId) {
        qb.where('id', '<', lastId)
      }
      return qb.orderBy('id', 'desc').limit(20)
    },
    myComments: async (parent, { page }, { account, loaders }) => {
      // todo 这里要优化，和小程序界面也没有匹配
      const qb = kn('comment').where({ userId: account.id }).select('statusId', kn.raw('GROUP_CONCAT(id) as ids'))
      if (page) qb.offset((page - 1) * 20)
      const temps = await qb.groupBy('statusId').orderBy(kn.raw('max(id)'), 'desc').limit(20)
      if (temps.length === 0) return []
      const statusIds = temps.map(i => i.statusId)
      const myCommentIds = temps.reduce((memo, i) => memo.concat(i.ids.split(',').map(i => parseInt(i))), [])
      const parents = await kn('comment_parent').whereIn('commentId', myCommentIds).select()
      const commentId2TopId = parents.reduce((memo, item) => ({
        ...memo,
        [item.commentId]: memo[item.commentId] || item.parentId
      }), {})
      const status = await loaders.statusLoader.loadMany(statusIds)
      const allCommentId = _.uniq(parents.map(i => i.parentId).concat(myCommentIds))
      let comments = await loaders.commentLoader.loadMany(allCommentId)
      comments = _.orderBy(comments, 'id', 'desc')
      const commentsGroupedByStatus = {}
      const commentsGroupedByParent = {}
      comments.forEach(comment => {
        if (comment.parentId) {
          const pid = commentId2TopId[comment.id]
          if (!commentsGroupedByParent[pid]) commentsGroupedByParent[pid] = []
          commentsGroupedByParent[pid].unshift(comment)
        } else {
          if (!commentsGroupedByStatus[comment.statusId]) commentsGroupedByStatus[comment.statusId] = []
          commentsGroupedByStatus[comment.statusId].push(comment)
        }
      })
      return status.map(i => ({
        id: i.id,
        status: i,
        comments: commentsGroupedByStatus[i.id].map(c => ({ ...c, replies: commentsGroupedByParent[c.id] || [] }))
      }))
    },
    myNotifications: async (parent, { category, lastId }, { account }) => {
      const qb = kn('notification').where({ userId: account.id })
      if (category) qb.where({ category })
      if (lastId) qb.where('id', '<', lastId)
      return qb.orderBy('id', 'desc').limit(20)
    },
    myUnreadNotificationsCount: async (parent, args, { account }) => {
      const count = await kn('notification').where({ userId: account.id, isRead: false }).count({ count: 'id' }).first()
      return count.count
    },
    recommended: async (parent, args, { app }) => {
      const products = await kn('product').join('product_app', 'id', 'productId').where({ isRecommended: 1, app }).orderBy('id', 'desc').limit(6)
      const recommended = products.map(i => ({ name: i.name, price: i.price, cover: i.cover, desc: i.desc, key: 'product_' + hashID.encode(i.id), page: `/pages/shop/product/item?id=${hashID.encode(i.id)}` }))
      return recommended
    },
    recommendation: async (parent, { category }, { app, account }) => {
      if (category === 'home' && account) {
        const service = await kn('shen_service').where({ userId: account.id }).where('end', '>=', kn.raw('CURRENT_DATE')).select('id').first()
        if (service) return []
      }
      return kn('recommendation').where({ app, category }).orderBy('orderNum').select()
    },
    product: async (parent, { id }, { app }) => {
      const product = await kn('product').where({ id }).first()
      const productApps = await kn('product_app').where({ productId: id }).select('app')
      if (!product || !productApps.some(i => i.app === app)) throw new GraphQLError('商品不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      const skus = await kn('product_sku').where({ productId: product.id })
      return { ...product, skus }
    },
    package: async (parent, { id }, { app, loaders }) => {
      const pack = await loaders.packageLoader.load(id)
      if (!pack || pack.status !== 1 || !pack.app === app) throw new GraphQLError('商品不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      return pack
    },
    myDefaultAddress: async (parent, args, { account }) => {
      return kn('address').where({ userId: account.id, isDefault: 1 }).first()
    },
    myAddresses: async (parent, args, { account }) => {
      return kn('address').where({ userId: account.id }).orderBy([{ column: 'isDefault', order: 'desc' }, { column: 'id', order: 'asc' }])
    },
    orderCheckout: async (parent, { products, packageId, courseId }, { app, loaders, account }) => {
      const isPackage = !!packageId
      if (courseId) {
        const course = await loaders.courseLoader.load(courseId)
        if (!course || course.status !== 1) throw new GraphQLError('课程信息错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        return {
          isPackage,
          needShipping: false,
          totalAmount: course.price,
          discountAmount: 0,
          finalAmount: course.price,
          course
        }
      } else if (packageId) {
        const pack = await loaders.packageLoader.load(packageId)
        if (!pack || pack.status !== 1 || !pack.app === app) throw new GraphQLError('套餐信息错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        return {
          isPackage,
          needShipping: true,
          // description: pack.name,
          totalAmount: pack.originalPrice,
          discountAmount: 0,
          finalAmount: pack.price,
          package: pack
        }
      } else {
        const prods = await loaders.productLoader.loadMany(products.map(i => i.productId))
        if (prods.some(i => !i) || prods.some(i => i.status !== 1) || prods.some(i => i.apps.indexOf(app) < 0)) throw new GraphQLError('商品信息错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const productsById = _.keyBy(prods, 'id')
        products = products.map(i => ({ ...productsById[i.productId], quantity: i.quantity }))
        const amount = products.reduce((memo, i) => memo + i.price * i.quantity, 0)
        return {
          isPackage: false,
          needShipping: products.some(i => i.needShipping),
          // description: products.length > 1 ? products[0].name + '等' + products.length + '件商品' : products[0].name,
          totalAmount: amount,
          discountAmount: 0,
          finalAmount: amount,
          products
        }
      }
    },
    order: async (parent, { id }, { app, loaders, account }) => {
      const order = await loaders.orderLoader.load(id)
      if (!order || order.app !== app || order.userId !== account.id) throw new GraphQLError('订单不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      return order
    },
    orders: async (parent, { status, shippingStatus, lastId }, { account }) => {
      const qb = kn('order').where({ userId: account.id })
      if (status !== null && status >= 0) {
        qb.where({ status })
      } else {
        qb.where('status', '>', -1)
      }
      if (lastId) qb.where('id', '<', lastId)
      return qb.orderBy('id', 'desc').limit(20)
    },
    ordersCountInfo: async (parent, args, { account }) => {
      const rows = await kn('order').where({ userId: account.id }).whereIn('status', [0, 1, 2]).groupBy('status').count('id as count').select('status')
      return {
        waitingPay: rows.find(i => i.status === 0)?.count ?? 0,
        waitingShip: rows.find(i => i.status === 1)?.count ?? 0,
        waitingReceive: rows.find(i => i.status === 2)?.count ?? 0
      }
    },
    plans: async (parent, { status, lastId }, { account }) => {
      const qb = kn('plan').where({ userId: account.id })
      if (status !== null) {
        qb.where({ status })
      }
      if (lastId) qb.where('id', '<', lastId)
      return qb.orderBy('id', 'desc').limit(20)
    },
    plan: async (parent, { id }, { account }) => {
      const plan = await kn('plan').where({ id }).first()
      if (!plan || plan.userId !== account.id) throw new GraphQLError('计划不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      return plan
    },
    progressingPlan (parent, { profileId }, { account, loaders }) {
      if (!profileId) {
        profileId = account.profiles[0].id
      } else if (!account.profiles.find(i => i.id === profileId)) {
        throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      }
      return loaders.progressingPlanLoader.load(profileId)
    },
    plansCountInfo: async (parent, args, { account }) => {
      const { count } = await kn('plan').where({ userId: account.id, status: 0 }).count('id as count').first()
      return { waitingOpen: count }
    },
    planTarget: async (parent, { profileId, weight, dates }, { account }) => {
      if (dates !== 28 && dates !== 56 && dates !== 84) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      const profile = account.profiles.find(i => i.id === profileId)
      if (!profile) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      const targets = targetWeight(dates, profile.height, weight)
      const tWeight = targets[targets.length - 1].targetWeight
      const tLose = round(weight - tWeight, 2)
      return {
        startWeight: weight,
        targetWeight: tWeight,
        targetLose: tLose,
        dates,
        targets: targets.map(i => i.targetWeight)
      }
    },
    howMuchCanILose: async (parent, { profileId, dates }, { account }) => {
      if (!dates) dates = 28
      if (!account) return null
      const profile = !profileId ? account.profiles[0] : account.profiles.find(i => i.id === profileId)
      if (!profile) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (dates !== 28) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (!profile.weight) return null
      const targets = targetWeight(dates, profile.height, profile.weight)
      return targets[targets.length - 1].targetWeight
    },
    howMuchCanILoses: async (parent, { profileId }, { account }) => {
      if (!account) return null
      const profile = !profileId ? account.profiles[0] : account.profiles.find(i => i.id === profileId)
      if (!profile) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (!profile.weight) return null
      const dates = [28, 84]
      return dates.map(i => {
        const targets = targetWeight(i, profile.height, profile.weight)
        return [i, targets[targets.length - 1].targetWeight]
      })
    },
    benefitAfterLose: async (parent, { profileId }, { account }) => {
      if (!account) return null
      if (!profileId) profileId = account.profiles[0].id
      const profile = account.profiles.find(i => i.id === profileId)
      if (!profile) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (!profile.weight) return null
      const bmi = profile.bmi
      const age = dayjs().diff(dayjs(profile.birth), 'year')
      if (bmi >= 22 && bmi <= 46) {
        const d1 = parseFloat(((bmi - 22) * 4168.168).toFixed(2))
        const d2 = parseFloat((d1 / 1681.68).toFixed(2))
        const d3 = parseFloat((d1 / 2681.68).toFixed(2))
        const d4 = parseFloat((0.0066 * (22 * 22 * 22 - bmi * bmi * bmi) - 0.5933 * (22 * 22 - bmi * bmi) + 14.504 * (22 - bmi)).toFixed(2))
        const d5 = parseFloat((-0.0151 * (bmi * bmi * bmi - 22 * 22 * 22) + 1.3765 * (bmi * bmi - 22 * 22) - 35.204 * (bmi - 22)).toFixed(2))
        const d6 = parseFloat((0.1138 * (bmi * bmi - 22 * 22) - 2.9 * (bmi - 22)).toFixed(2))
        const d7 = parseFloat(((d6 * (88 - age) * 365) / 48 / 365).toFixed(2))
        const d8 = parseFloat(((d4 + d7) * 0.427).toFixed(2))
        const d9 = parseFloat((0.0041 * (22 * 22 * 22 - bmi * bmi * bmi) - 0.371 * (22 * 22 - bmi * bmi) + 7.9679 * (22 - bmi)).toFixed(2))
        return [d1, d2, d3, d4, d5, d6, d7, d8, d9]
      }
      return null
    },
    qiniuToken: async (parent, { prefix }, { account }) => {
      if (prefix !== 'avatar' && prefix !== 'status') return new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      return getUploadToken(account.id, prefix)
    },
    shareConfig: (p, args, { app }) => {
      const shares = {
        tqq: {
          title: '体轻轻｜打卡减重，我看行',
          imageUrl: 'https://imgs.tiqingqing.com/share/tqq0911-share.jpg',
          iCanLoseImage: 'https://imgs.tiqingqing.com/share/icanlose0910-preview.jpg'
        },
        shen: {
          title: '沈教授的代谢训练营',
          imageUrl: 'https://imgs.tiqingqing.com/share/shen0410-share.jpg',
          iCanLoseImage: 'https://imgs.tiqingqing.com/share/icanlose0910-preview.jpg'
        }
      }
      return shares[app]
    },
    messageSubscribe: async (parent, args, { account, app }) => {
      const user = await kn('user').where({ id: account.id }).first()
      const templateId = app === 'tqq' ? '0MQ7SEpol5yLIzD2eTjHSXv4GuadVtg88XlrnW8I5Tw' : 'LZiiQwZNXH-r4P6zDH7wqdbAI4Ehs0HxO6nmghmA7YM'
      const sub = await kn('user_subscribe_templates').where({ userOpenId: account.openId, templateId }).first()
      return {
        frequency: user.subFrequency,
        hour: user.subHour,
        dayOrDate: user.subDayOrDate,
        count: sub ? sub.times : 0
      }
    },
    // inShenService: async (parent, args, { account, app }) => {
    //   if (app !== 'shen') return false
    //   const service = await kn('shen_service').where({ userId: account.id }).where('end', '>=', kn.raw('CURRENT_DATE')).select('id').first()
    //   return !!service
    // },
    shenServicePendingPayback: async (parent, args, { account, app }) => {
      if (app !== 'shen') return null
      const serviceList = await kn('shen_service').where({ userId: account.id }).select(['id', 'paybackEverydayAmount'])
      if (!serviceList.length) return null
      let pendingPaybackAmount = 0
      let pendingPaybackDays = 0
      for (const service of serviceList) {
        // console.log(service)
        const list = await kn('shen_service_day').where({ status: 1 }).where('shenServiceId', service.id).select()
        // console.log(list)
        pendingPaybackDays += list.length
        pendingPaybackAmount += list.length * service.paybackEverydayAmount
      }
      if (pendingPaybackDays === 0) return null
      // console.log(`shen service pending payback account: ${account.id} ${pendingPaybackAmount}, ${pendingPaybackDays}`)
      return { pendingPaybackAmount, pendingPaybackDays, shenServiceName: 'HEALTH积优生活教育' }
    },
    mpReferralQrCode: async (parent, args, { account, app }) => {
      const env = config.get('mp.env') || 'develop'
      const key = `referral/${env}/${hashID.encode(account.id)}`
      const url = `${imageHost}${key}-referral.jpg`
      const exists = await checkFileExists(key)
      if (exists) return url
      const mp = new MPApi(app)
      const page = 'pages/welcome/can-i-lose'
      const qrBuffer = await mp.getMiniBarCodeUnlimited({
        page,
        scene: `referrerUserId=${hashID.encode(account.id)}`,
        env_version: env
      })
      const stream = new Readable()
      stream.push(qrBuffer)
      stream.push(null)
      await uploadImage(key, stream)
      return url
    },
    courses: async (parent, args) => {
      return kn('course').where('status', 1).select()
    },
    course: async (parent, { id }) => {
      const item = await kn('course').where({ id }).first()
      if (!item) throw new GraphQLError('课程不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (item.status !== 1) throw new GraphQLError('课程已下架', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      return item
    },
    myCourses: async (parent, args, { account }) => {
      const list = await kn('user_course').where({ userId: account.id }).select()
      return list
    }
  },
  Mutation: {
    register: async (parent, { referrerUserId, mobileCode }, { app, account }) => {
      const mp = new MPApi(app)
      // 微信获取电话号码注册
      const mobileInfo = await mp.code2mobile(mobileCode)
      const mobile = mobileInfo.mobile
      const mobileCountry = mobileInfo.mobileCountry
      await kn.transaction(async trx => {
        const user = trx('user').where({ id: account.id }).first()
        const updates = { mobile, mobileCountry }
        if (!user.referrerUserId && referrerUserId) {
          const u = await trx('user').where({ id: referrerUserId }).first()
          if (u) updates.referrerUserId = referrerUserId
        }
        await trx('user').where({ id: account.id }).update(updates)
      })
    },
    completeProfile: async (parent, args, { account, app }) => {
      if (account.name) return
      const { weight, ...restArgs } = args
      restArgs.name = restArgs.name.replace(/\s+/g, '')
      if (!restArgs.name) throw new GraphQLError('姓名不能为空', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      restArgs.heightAt = kn.raw('CURRENT_TIMESTAMP')
      if (restArgs.target) restArgs.targetAt = kn.raw('CURRENT_TIMESTAMP')
      const userUpdates = _.pick(restArgs, ['name', 'avatar', 'gender'])
      const profileId = account.profiles[0].id
      await kn.transaction(async function (trx) {
        const user = await trx('user').where({ id: account.id }).select('id', 'name')
        if (user.name) return
        await trx('user').where({ id: account.id }).update(userUpdates)
        await trx('user_profile').where({ id: profileId }).update({ ...restArgs })
        await trx('user_profile_history').insert({ ..._.pick(restArgs, ['height', 'gender', 'birth', 'target']), profileId })
      })
      if (weight) {
        await createStatus(app, account, { profileId, type: 'weight', weight: { weight } })
      }
    },
    saveProfile: async (parent, args, { account }) => {
      const { id, ...restArgs } = args
      if (_.isEmpty(restArgs)) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (id) {
        const profile = _.find(account.profiles, { id })
        if (!profile) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        // 没有调用过 completeProfile
        if (!profile.name) throw new GraphQLError('请先完善个人信息', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const info = restArgs.height && restArgs.height !== profile.height ? { ...restArgs, heightAt: kn.raw('CURRENT_TIMESTAMP') } : restArgs
        if (restArgs.target && (!profile.target || restArgs.target !== profile.target)) info.targetAt = kn.raw('CURRENT_TIMESTAMP')
        await kn.transaction(async function (trx) {
          if (profile.self) {
            const userUpdate = _.pick(restArgs, ['name', 'avatar', 'gender'])
            if (!_.isEmpty(userUpdate)) {
              await trx('user').where({ id: account.id }).update(userUpdate)
            }
          }
          await trx('user_profile').where({ id }).update(info)
        })
      } else {
        if (!restArgs.avatar || !restArgs.name || !restArgs.gender || !restArgs.height || !restArgs.birth) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const params = { ...restArgs, userId: account.id, status: 0, heightAt: kn.raw('CURRENT_TIMESTAMP') }
        if (restArgs.target) params.targetAt = kn.raw('CURRENT_TIMESTAMP')
        await kn('user_profile').insert(params)
      }
    },
    deleteProfile: async (parent, { id }, { account }) => {
      const profile = await kn('user_profile').where({ id }).first()
      if (profile.self) throw new GraphQLError('不能删除自己的档案', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (profile.userId !== account.id) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      await kn.transaction(async function (trx) {
        await kn('user_profile').where({ id }).update({ deleted: true })
        await kn('day_weight').where({ profileId: id }).delete()
        await kn('status').where({ profileId: id }).update({ deleted: true, deleteAt: kn.raw('CURRENT_TIMESTAMP') })
      })
    },
    saveAddress: async (parent, { id, ...restArgs }, { account }) => {
      const count = await kn('address').where({ userId: account.id }).count('id as count').first()
      restArgs.isDefault = restArgs.isDefault ? 1 : 0
      if (id) {
        const address = await kn('address').where({ id }).first()
        if (!address || address.userId !== account.id) throw new GraphQLError('地址不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
        await kn.transaction(async function (trx) {
          if (restArgs.isDefault) {
            await trx('address').where({ userId: account.id }).update({ isDefault: 0 })
          }
          await trx('address').where({ id }).update(restArgs)
        })
        return { id, ...restArgs }
      } else {
        if (count === 0) restArgs.isDefault = 1
        if (count >= 50) throw new GraphQLError('地址数量超过限制', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const insertId = await kn.transaction(async function (trx) {
          if (restArgs.isDefault) {
            await trx('address').where({ userId: account.id }).update({ isDefault: 0 })
          }
          const [id] = await trx('address').insert({ ...restArgs, userId: account.id })
          return id
        })
        return { id: insertId, ...restArgs }
      }
    },
    createStatus: async (__, args, { account, app }) => {
      await createStatus(app, account, args)
    },
    changeStatusPublic: async (__, { id, isPublic }, { account }) => {
      const status = await kn('status').where({ id }).first()
      if (!status || status.userId !== account.id) throw new GraphQLError('内容不存在或权限不足', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      await kn('status').where({ id }).update({ isPublic })
      return true
    },
    deleteStatus: async (__, { id }, { account, app }) => {
      await removeStatus(app, account, id)
    },
    follow: async (_, { userId }, { account }) => {
      const user = await kn('user').where({ id: userId }).first()
      if (!user) throw new GraphQLError('用户不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      await kn.transaction(async function (trx) {
        const exit = await kn('user_follower').where({ followerUserId: account.id, userId }).first()
        if (exit) return
        await trx('user_follower').insert({ followerUserId: account.id, userId })
        await trx('follow_event').insert({ followerId: account.id, userId, eventType: 'follow' })
        await trx('user').where({ id: userId }).update({ followerCount: kn.raw('followerCount + 1') })
        await trx('user').where({ id: account.id }).update({ followingCount: kn.raw('followingCount + 1') })
        await trx('notification').insert({ category: 'follow', entityType: 'user', entityId: userId, senderId: account.id, userId })
      })
      return true
    },
    unFollow: async (_, { userId }, { account }) => {
      const user = await kn('user').where({ id: userId }).first()
      if (!user) throw new GraphQLError('用户不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      await kn.transaction(async function (trx) {
        const exit = await kn('user_follower').where({ followerUserId: account.id, userId }).first()
        if (!exit) return
        await trx('user_follower').where({ id: exit.id }).delete()
        await trx('follow_event').insert({ followerId: account.id, userId, eventType: 'unfollow' })
        await trx('user').where({ id: userId }).update({ followerCount: kn.raw('followerCount - 1') })
        await trx('user').where({ id: account.id }).update({ followingCount: kn.raw('followingCount - 1') })
        const notification = await trx('notification').where({ category: 'follow', entityType: 'user', entityId: userId, senderId: account.id, userId }).first()
        if (notification?.isRead === false) {
          trx('notification').where({ id: notification.id }).delete()
        }
      })
      return true
    },
    like: async (_, args, { account, app }) => {
      const { entityId, entityType } = args
      const entity = await kn(entityType).where({ id: entityId }).first()
      if (!entity) throw new GraphQLError('内容不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      const liked = await kn('like').where({ entityId, entityType, userId: account.id }).first()
      if (liked && !liked.deleted) return true
      await kn.transaction(async function (trx) {
        let likeId
        if (liked?.deleted) {
          await trx('like').where({ id: liked.id }).update({ deleted: false, updateAt: kn.raw('CURRENT_TIMESTAMP') })
          likeId = liked.id
        } else {
          [likeId] = await trx('like').insert({ app, entityId, entityType, userId: account.id })
        }
        if (entity.userId !== account.id) {
          const notification = await trx('notification').where({ category: 'like', likeId, senderId: account.id, userId: entity.userId }).first()
          if (!notification) {
            await trx('notification').insert({ category: 'like', likeId, senderId: account.id, userId: entity.userId })
          }
        }
        await trx(entityType).where({ id: entityId }).update({ likeCount: kn.raw('likeCount + 1') })
        await trx('user').where({ id: entity.userId }).update({ likeCount: kn.raw('likeCount + 1') })
      })
      return true
    },
    unLike: async (_, args, { account, app }) => {
      const { entityId, entityType } = args
      const entity = await kn(entityType).where({ id: entityId }).first()
      if (!entity) throw new GraphQLError('实体不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      const liked = await kn('like').where({ entityId, entityType, userId: account.id }).first()
      if (!liked || liked.deleted) return true
      await kn.transaction(async function (trx) {
        await trx('like').where({ id: liked.id }).update({ deleted: true, deletedAt: kn.raw('CURRENT_TIMESTAMP') })
        await trx(entityType).where({ id: entityId }).update({ likeCount: kn.raw('likeCount - 1') })
        await trx('user').where({ id: entity.userId }).update({ likeCount: kn.raw('likeCount - 1') })
        await trx('notification').where({ category: 'like', likeId: liked.id, senderId: account.id }).delete()
      })
      return true
    },
    comment: async (_, args, { account, app }) => {
      const { statusId, parentId, content } = args
      const status = await kn('status').where({ id: statusId }).first()
      if (!status) throw new GraphQLError('状态不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      let parent = null
      let parents = []

      if (parentId) {
        parent = await kn('comment').where({ id: parentId }).first()
        const ps = await kn('comment_parent').where({ commentId: parentId }).select()
        parents = ps.map(i => i.parentId)
        if (!parent || parent.statusId !== status.id) throw new GraphQLError('父评论不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      }
      const mp = new MPApi(app)
      const warning = await mp.checkTextRisky({ content, scene: 2, openId: account.openId })
      if (warning) throw new GraphQLError('内容存在风险', { extensions: { code: httpErrorCode.BAD_REQUEST } })

      await kn.transaction(async function (trx) {
        const [commentId] = await trx('comment').insert({ app, statusId, parentId: parentId || 0, userId: account.id, content })
        await trx('status').where({ id: statusId }).update({ replyCount: kn.raw('replyCount + 1') })

        if (parent) {
          const allParents = [...parents, parentId]
          await trx('comment_parent').insert(allParents.map(i => ({ commentId, parentId: i })))
          // 给被回复的用户发送消息, 但是不给自己发
          if (account.id !== parent.userId) {
            await trx('notification').insert({ category: 'comment', commentId, replyCommentId: parent.id, senderId: account.id, userId: parent.userId })
          }
          // 给被回复的用户的父级(顶级)评论用户发送消息
          let grandParentUserId = 0
          if (allParents.length > 1) {
            const grandParent = await trx('comment').where({ id: allParents[0] }).first()
            if (account.id !== grandParent.userId && grandParent.userId !== parent.userId) {
              grandParentUserId = grandParent.userId
              await trx('notification').insert({ category: 'comment', commentId, replyCommentId: grandParent.id, senderId: account.id, userId: grandParent.userId })
            }
          }
          // 给打卡用户发送通知
          if (account.id !== status.userId && status.userId !== parent.userId && status.userId !== grandParentUserId) {
            await trx('notification').insert({ category: 'comment', commentId, senderId: account.id, userId: status.userId })
          }
        } else {
          // 给打卡用户发送通知
          if (account.id !== status.userId) {
            await trx('notification').insert({ category: 'comment', commentId, senderId: account.id, userId: status.userId })
          }
        }
      })
      return true
    },
    deleteComment: async (_, args, { account, app }) => {
      const { id } = args
      const item = await kn('comment').where({ id }).first()
      if (!item || item.userId !== account.id) throw new GraphQLError('评论不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      await kn.transaction(async function (trx) {
        await trx('comment').where({ id }).update({ deleted: 1, deletedAt: kn.raw('CURRENT_TIMESTAMP') })
        await trx('status').where({ id: item.statusId }).update({ replyCount: kn.raw('replyCount - 1') })
        await trx('notification').where({ category: 'comment', commentId: item.id, senderId: account.id }).delete()
      })
      return true
    },
    readNotification: async (root, { maxId }, { account }) => {
      const qb = kn('notification').where({ userId: account.id, isRead: false })
      if (maxId) qb.where('id', '<=', maxId)
      await qb.update({ isRead: true, readAt: kn.raw('current_timestamp()') })
      return true
    },
    createOrder: async (parent, { products, packageId, addressId, courseId }, { account, app, loaders }) => {
      function genOrderNoAndOutTradeNo (orderId) {
        const orderNo = `${dayjs().format('YYMMDD')}${parseInt(orderHashID.encode(orderId), 16)}`
        const outTradeNo = `${orderPrefix}${orderNo}`
        return { orderNo, outTradeNo }
      }
      let info = null
      let address = null
      if (addressId) {
        address = await loaders.addressLoader.load(addressId)
        if (!address || address.userId !== account.id) throw new GraphQLError('收货地址错误', { extensions: { code: httpErrorCode.NOT_FOUND } })
      }
      if (products && products.length > 0) {
        const productIds = products.map(i => i.productId)
        const ps = await loaders.productLoader.loadMany(productIds)
        if (ps.some(i => !i) || ps.some(i => i.apps.indexOf(app) < 0) || ps.some(i => i.status !== 1)) throw new GraphQLError('商品信息错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const needShipping = ps.some(i => i.needShipping)
        if (needShipping && !address) throw new GraphQLError('没有收货地址', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const productsById = _.keyBy(products, 'productId')
        products = ps.map(i => ({ ...i, quantity: productsById[i.id].quantity }))
        const amount = products.reduce((memo, i) => memo + i.price * i.quantity, 0)
        info = await kn.transaction(async trx => {
          const data = {
            app,
            userId: account.id,
            status: 0,
            description: products.length > 1 ? products[0].name + '等' + products.length + '件商品' : products[0].name,
            totalAmount: amount,
            discountAmount: 0,
            finalAmount: amount,
            needShipping
          }
          if (needShipping) {
            data.recipient = { addressId: address.id, name: address.name, phone: address.phone, province: address.province, city: address.city, county: address.county || '', town: address.town || '', address: address.address }
          }
          const [orderId] = await trx('order').insert(data)
          const { orderNo, outTradeNo } = genOrderNoAndOutTradeNo(orderId)
          await trx('order').where({ id: orderId }).update({ orderNo, outTradeNo })
          await trx('order_product').insert(ps.map(i => ({ orderId, productId: i.id, name: i.name, cover: i.cover, originalPrice: i.price, finalPrice: i.price, quantity: i.quantity })))
          return { orderId, outTradeNo, description: data.description, amount: data.finalAmount }
        })
      } else if (packageId) {
        if (!address) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const pack = await loaders.packageLoader.load(packageId)
        if (!pack || pack.app !== app) throw new GraphQLError('套餐信息错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        info = await kn.transaction(async trx => {
          const [orderId] = await trx('order').insert({
            app,
            userId: account.id,
            status: 0,
            payStatus: 0,
            description: pack.name,
            totalAmount: pack.price,
            discountAmount: 0,
            finalAmount: pack.price,
            needShipping: 1,
            packageId: pack.id,
            packageName: pack.name,
            recipient: { addressId: address.id, name: address.name, phone: address.phone, province: address.province, city: address.city, county: address.county || '', town: address.town || '', address: address.address }
          })
          const { orderNo, outTradeNo } = genOrderNoAndOutTradeNo(orderId)
          await trx('order').where({ id: orderId }).update({ orderNo, outTradeNo })
          await trx('order_product').insert(pack.products.map(i => ({
            orderId,
            productId: i.id,
            name: i.name,
            cover: i.cover,
            originalPrice: i.originalPrice,
            discountedPrice: i.price,
            finalPrice: i.price,
            quantity: i.quantity
          })))
          return { orderId, outTradeNo, description: pack.name, amount: pack.price }
        })
      } else if (courseId) {
        const course = await kn('course').where({ id: courseId }).first()
        if (!course || course.status !== 1) throw new GraphQLError('课程信息错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        const userCourse = await kn('user_course').where({ userId: account.id, courseId }).first()
        if (userCourse) throw new GraphQLError('已购买该课程', { extensions: { code: httpErrorCode.BAD_REQUEST } })
        info = await kn.transaction(async trx => {
          const [orderId] = await trx('order').insert({
            app,
            userId: account.id,
            status: 0,
            payStatus: 0,
            description: course.name,
            totalAmount: course.price,
            discountAmount: 0,
            finalAmount: course.price,
            courseId: course.id
          })
          const { orderNo, outTradeNo } = genOrderNoAndOutTradeNo(orderId)
          await trx('order').where({ id: orderId }).update({ orderNo, outTradeNo })
          return { orderId, outTradeNo, description: course.name, amount: course.price }
        })
      }
      if (!info) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      const prepayId = await createPrepayOrder(app, { ...info, openId: account.openId })
      await redis.set(`prepay:${info.orderId}:${account.openId}`, prepayId, 'EX', 60 * 60 * 2)
      return getPrepayInfo(app, prepayId)
    },
    prepayOrder: async (parent, { orderId }, { account, app }) => {
      const order = await kn('order').where({ id: orderId }).first()
      if (!order || order.userId !== account.id) throw new GraphQLError('订单不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (order.status !== 0) throw new GraphQLError('订单状态不可支付', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      let prepayId = await redis.get(`prepay:${orderId}:${account.openId}`)
      if (!prepayId) {
        prepayId = await createPrepayOrder(app, { orderId, description: order.description, amount: order.finalAmount, openId: account.openId })
        await redis.set(`prepay:${orderId}:${account.openId}`, prepayId, 'EX', 60 * 60 * 2)
      }
      return getPrepayInfo(app, prepayId)
    },
    completeOrder: async (parent, { id }, { account }) => {
      const order = await kn('order').where({ id }).first()
      if (!order || order.userId !== account.id) throw new GraphQLError('订单不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (order.status !== 1) throw new GraphQLError('订单状态不正确', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      await kn('order').where({ id }).update({ status: 10, completeAt: kn.raw('CURRENT_TIMESTAMP') })
      return true
    },
    deleteOrder: async (parent, { id }, { account, loaders }) => {
      // const order = await kn('order').where({ id }).first()
      const order = await loaders.orderLoader.load(id)
      if (!order || order.userId !== account.id) throw new GraphQLError('订单不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (order.status !== 0) throw new GraphQLError('订单状态不能删除', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      await kn('order').where({ id }).update({ status: -1, deleteAt: kn.raw('CURRENT_TIMESTAMP') })
    },
    startPlan: async (parent, { profileId, id, startWeight, startDate }, { account }) => {
      const plan = await kn('plan').where({ id }).first()
      if (!profileId) profileId = account.profiles[0].id
      const profile = account.profiles.find(i => i.id === profileId)
      if (!profile) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (!plan || plan.userId !== account.id) throw new GraphQLError('计划不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (plan.status !== 0) throw new GraphQLError('计划状态不正确', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      await kn.transaction(async function (trx) {
        const targets = targetWeight(plan.dates, profile.height, startWeight)
        const startAt = startDate === 'tomorrow' ? dayjs({ hour: 0 }).add(1, 'day') : dayjs({ hour: 0 })
        const updates = {
          profileId,
          status: 1,
          startDate: startAt.format('YYYY-MM-DD'),
          endDate: dayjs(startAt).add(plan.dates - 1, 'days').format('YYYY-MM-DD'),
          startWeight,
          targetWeight: targets[targets.length - 1].targetWeight,
          targetLose: round(startWeight - targets[targets.length - 1].targetWeight, 2),
          startAt: kn.raw('CURRENT_TIMESTAMP')
        }
        await trx('plan').where({ id }).update(updates)
        const values = targets.map((i, index) => ({
          dateIndex: index + 1,
          planId: id,
          targetWeight: i.targetWeight,
          targetLose: i.targetLose,
          date: startAt.add(index, 'day').format('YYYY-MM-DD')
        }))
        await trx('plan_target').insert(values)
      })
      return true
    },
    changePlanWeight: async (parent, { id, weight }, { account }) => {
      const plan = await kn('plan').where({ id }).first()
      if (!plan || plan.userId !== account.id) throw new GraphQLError('计划不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (plan.status !== 1) throw new GraphQLError('计划状态不正确', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      const profile = account.profiles.find(i => i.id === plan.profileId)
      const targets = targetWeight(plan.dates, profile.height, weight)
      const today = dayjs({ hour: 0 })
      const days = today.diff(dayjs(plan.startDate), 'day')
      if (days > 3) throw new GraphQLError('已经超过4天，不能修改初始体重', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      await kn.transaction(async function (trx) {
        await trx('plan').where({ id }).update({ startWeight: weight, targetWeight: targets[targets.length - 1].targetWeight, targetLose: round(weight - targets[targets.length - 1].targetWeight, 2) })
        const values = targets.map((i, index) => ({
          dateIndex: index + 1,
          targetWeight: i.targetWeight,
          targetLose: i.targetLose,
          date: dayjs(plan.startDate).add(index, 'day').format('YYYY-MM-DD')
        }))
        for (const v of values) {
          const { dateIndex, ...others } = v
          await trx('plan_target').where({ planId: id, dateIndex }).update(others)
        }
      })
      return true
    },
    planPayback: async (parent, { planId, date }, { account, app }) => {
      const plan = await kn('plan').where({ id: planId }).first()
      if (!plan || plan.userId !== account.id || !plan.isShenPaybackPlan) throw new GraphQLError('计划不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (!dayjs(date).isBetween(dayjs(plan.startDate), dayjs(plan.endDate), 'day', '[]')) {
        throw new GraphQLError('日期不在计划范围内', { extensions: { code: httpErrorCode.CUSTOM } })
      }
      const planTarget = await kn('plan_target').where({ planId, date }).first()
      if (!planTarget) throw new GraphQLError('计划目标不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (planTarget.shenPaybackStatus !== 1) return true
      await kn.transaction(async function (trx) {
        const order = await trx('order').where({ id: plan.orderId }).first()
        if (!order) return
        const refundNo = `${order.id}_${plan.id}_${dayjs(planTarget.date).format('YYYYMMDD')}_payback`
        const amount = Math.floor(order.finalAmount / plan.dates)
        const [id] = await trx('order_refund').insert({
          orderId: order.id,
          refundNo,
          amount,
          status: 'PROCESSING',
          createAt: kn.raw('CURRENT_TIMESTAMP')
        })
        await trx('plan_target').where({ id: planTarget.id }).update({ shenPaybackStatus: 2, refundId: id })
        const info = {
          transaction_id: order.payTransactionId,
          out_refund_no: refundNo,
          reason: '沈教授代谢训练营打卡退款',
          amount: {
            refund: amount,
            total: order.finalAmount,
            currency: 'CNY'
          },
          notify_url: refundNotifyUrl
        }
        const result = await requestRefund(app, info)
        const update = {
          status: result.status,
          wechatRefundId: result.refund_id,
          channel: result.channel,
          userReceivedAccount: result.user_received_account
        }
        if (result.status === 'SUCCESS') {
          update.updateAt = dayjs(result.success_time).format('YYYY-MM-DD HH:mm:ss')
        }
        await trx('order_refund').where({ id }).update(update)
      })
      return true
    },
    planReward: async (parent, { planId, date, userId }, { account }) => {
      const plan = await kn('plan').where({ id: planId }).first()
      if (!plan || plan.userId !== account.id || !plan.isShenPaybackPlan) throw new GraphQLError('计划不存在或权限不足', { extensions: { code: httpErrorCode.NOT_FOUND } })
      const user = await kn('user').where({ id: userId }).first()
      if (!user) throw new GraphQLError('用户不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      const me = await kn('user').where({ id: account.id }).first()
      const ids = me.referrerUserId ? [me.referrerUserId, 10684, 10659] : [10684, 10659]
      if (ids.indexOf(userId) === -1) throw new GraphQLError('用户不在奖励名单中', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (!dayjs(date).isBetween(dayjs(plan.startDate), dayjs(plan.endDate), 'day', '[]')) {
        throw new GraphQLError('日期不在计划范围内', { extensions: { code: httpErrorCode.CUSTOM } })
      }
      const planTarget = await kn('plan_target').where({ planId, date }).first()
      if (!planTarget) throw new GraphQLError('计划目标不存在', { extensions: { code: httpErrorCode.NOT_FOUND } })
      if (planTarget.shenPaybackStatus !== 1) return true
      await kn.transaction(async function (trx) {
        const order = await trx('order').where({ id: plan.orderId }).first()
        if (!order) return
        const amount = Math.floor(order.finalAmount / plan.dates)
        const [id] = await trx('order_commission').insert({ orderId: order.id, userId: user.id, amount, status: 'WAITING' })
        await trx('plan_target').where({ id: planTarget.id }).update({ shenPaybackStatus: 3, commissionId: id })
      })
      // 给目标发送提醒
      try {
        const qywxUser = await kn('qywx_user').where({ mobile: user.mobile }).first()
        if (!qywxUser) return
        sendText(`收到打赏，${account.name}将${account.gender === 1 ? '他' : '她'}的打卡退款份额打赏给你，系统将在计划结束后结算`, qywxUser.userId)
      } catch (error) {
        console.error(error)
      }
    },
    messageSubscribe: async (parent, { hour, frequency, dayOrDate }, { account }) => {
      console.log(hour, frequency, dayOrDate)
      if (frequency && (['daily', 'weekly', 'monthly'].indexOf(frequency) < 0)) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (frequency && frequency !== 'daily' && (dayOrDate === null)) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      if (hour && (hour < 6 || hour > 23 || (hour > 10 && hour < 18))) throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })

      if (frequency === 'daily') {
        dayOrDate = null
      } else if (frequency === 'weekly' && (dayOrDate > 6 || dayOrDate < 0)) {
        throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      } else if (frequency === 'monthly' && [-2, -1, 1, 2, 15].indexOf(dayOrDate) < 0) {
        throw new GraphQLError('参数错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      }
      const update = {}
      if (hour) update.subHour = hour
      if (frequency) {
        update.subFrequency = frequency
        update.subDayOrDate = dayOrDate
      }
      if (_.isEmpty(update)) return false
      await kn('user').where({ id: account.id }).update(update)
      return true
    },
    shenServicePayback: async (parent, { action }, { account, app }) => {
      if (app !== 'shen') return false
      const serviceList = await kn('shen_service').where({ userId: account.id }).select('id', 'orderId')
      if (!serviceList.length) return false
      const list = await kn('shen_service_day').where({ status: 1 }).whereIn('shenServiceId', serviceList.map(i => i.id)).select()
      if (!list.length) return false
      if (action !== 2 && action !== 3) return false
      await kn('shen_service_day').whereIn('id', list.map(i => i.id)).update({ status: action })
      return true
      // if (action === 3) {
      //   // 奖励给沈老师
      //   await kn('shen_service_day').whereIn('id', list.map(i => i.id)).update({ status: 3 })
      //   return true
      // }
      // // 退款
      // await kn.transaction(async function (trx) {
      //   const listGrouped = _.groupBy(list, 'shenServiceId')
      //   for (const serviceId in listGrouped) {
      //     const service = _.find(serviceList, i => i.id === parseInt(serviceId))
      //     const days = listGrouped[serviceId]
      //     const amount = days.length * 618
      //     if (service.orderId) {
      //       // 退款
      //       const order = await trx('order').where({ id: service.orderId }).first()
      //       const refundNo = `${order.id}_${list.map(i => i.id).join('_')}_service_payback`
      //       const [refundId] = await trx('order_refund').insert({
      //         orderId: order.id,
      //         refundNo,
      //         amount,
      //         status: 'PROCESSING',
      //         createAt: kn.raw('CURRENT_TIMESTAMP')
      //       })
      //       await trx('shen_service_day').whereIn('id', days.map(d => d.id)).update({ status: 2, refundId })
      //       const info = {
      //         transaction_id: order.payTransactionId,
      //         out_refund_no: refundNo,
      //         reason: 'HEALTH积优生活教育打卡退款',
      //         amount: {
      //           refund: amount,
      //           total: order.finalAmount,
      //           currency: 'CNY'
      //         },
      //         notify_url: refundNotifyUrl
      //       }
      //       const result = await requestRefund(app, info)
      //       const update = {
      //         status: result.status,
      //         wechatRefundId: result.refund_id,
      //         channel: result.channel,
      //         userReceivedAccount: result.user_received_account
      //       }
      //       if (result.status === 'SUCCESS') {
      //         update.updateAt = dayjs(result.success_time).format('YYYY-MM-DD HH:mm:ss')
      //       }
      //       await trx('order_refund').where({ id: refundId }).update(update)
      //     } else {
      //       // 转账
      //       const appId = config.get('mp').shen.appID
      //       const sceneId = '1000'
      //       const batchName = `HEALTH积优生活教育打卡${days.length === 1 ? dayjs(days[0].date).format('YY-MM-DD') : dayjs(days[0].date).format('YY-MM-DD') + '至' + dayjs(days[days.length - 1].date).format('YY-MM-DD')}`
      //       const batchRemark = batchName
      //       const outBatchNo = 'jylifex' + service.id + 'x' + days.map(i => i.id).join('x')
      //       const [transferId] = await trx('wechat_pay_transfer').insert({
      //         appId,
      //         outBatchNo,
      //         transferSceneId: sceneId,
      //         batchName,
      //         batchRemark,
      //         totalAmount: amount,
      //         totalNum: days.length,
      //         batchStatus: 'WAITING'
      //       })
      //       const details = days.map(i => ({
      //         transferId,
      //         openId: account.openId,
      //         outDetailNo: 'jylifeday' + i.id,
      //         transferAmount: 618,
      //         transferRemark: `HEALTH积优生活教育打卡${dayjs(i.date).format('YY-MM-DD')}`
      //       }))
      //       await trx('wechat_pay_transfer_detail').insert(details)
      //       await trx('shen_service_day').whereIn('id', days.map(d => d.id)).update({ status: 2, transferId })
      //       const transInfo = {
      //         appid: appId,
      //         out_batch_no: outBatchNo,
      //         batch_name: batchName,
      //         batch_remark: batchRemark,
      //         total_amount: amount,
      //         total_num: days.length,
      //         transfer_scene_id: '1000',
      //         notify_url: config.get('wechat-pay').transferNotifyUrl,
      //         transfer_detail_list: days.map(i => ({
      //           openid: account.openId,
      //           out_detail_no: 'jylifeday' + i.id,
      //           transfer_amount: 618,
      //           transfer_remark: `HEALTH积优生活教育打卡${dayjs(i.date).format('YY-MM-DD')}`
      //         }))
      //       }
      //       console.log(transInfo)
      //       const { batch_id: batchId, create_time: createTime, batch_status: batchStatus } = await transferToUser(transInfo)
      //       await trx('wechat_pay_transfer').where('id', transferId).update({ batchId, createTime, batchStatus })
      //     }
      //   }
      // })
      // return true
    }
  }
}
