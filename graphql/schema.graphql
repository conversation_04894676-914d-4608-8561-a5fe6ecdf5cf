scalar ID
scalar Avatar
scalar DateTime
scalar Birth
scalar Image
scalar QueryDate

directive @auth(profiled: Boolean = false) on FIELD_DEFINITION
directive @refreshCache(returnAccount: Boolean = false) on FIELD_DEFINITION
directive @checkProfileId on FIELD_DEFINITION

enum StatusType {
    status
    weight
    meal
    sport
    measurement
    pressure
    glucose
    lipid
    acid
}
enum EntityType {
    status
    comment
    user
}

type AccountWithToken {
    token: String
    user: Account
}
type Account {
    id: ID
    name: String
    avatar: Avatar
    mobile: String
    gender: Int
    genderLabel: String
    profiles: [Profile]
}
type Profile {
    id: ID
    self: Boolean
    name: String
    avatar: Image
    gender: Int
    genderLabel: String
    birth: Birth
    age: String
    weightAt: DateTime
    weight: Float
    bmi: Float
    bf: Float
    changes: Float
    height: Float
    heightAt: DateTime
    target: Float
    lastWeight: ProfileLastWeight
}
type ProfileLastWeight {
    weight: Float
    date: DateTime
    changes: Float
}
type User {
    id: ID
    name: String
    avatar: Avatar
    gender: Int
    createAt: DateTime
    followerCount: Int
    followingCount: Int
    likeCount: Int
    introduction: String
    """ 3 关注我 2相互关注，1已关注 0 未关注 """
    followed: Int
    certification: String
}
type Like  {
    id: ID
    createAt: DateTime
    entityId: ID
    entityType: EntityType
    status: Status
    comment: Comment
    user: User
}
type MyComment {
    id: ID
    status: Status
    comments: [Comment]
}
type Notification {
    id: ID
    sender: User
    desc: String
    isRead: Boolean
    createAt: DateTime
    category: String
    like: Like
    comment: Comment
    replyComment: Comment
}
type Weight {
    date: DateTime
    previous: Boolean
    weight: Float
    bf: Float
    bmi: Float
    targetWeight: Float
}
type WeightMonthCount {
    dates: Int
    today: Int
    weightedDates: Int
}
type Status {
    id: ID
    app: String
    type: StatusType
    content: String
    images: [Image]
    emotion: String
    likeCount: Int
    replyCount: Int
    createAt: DateTime
    user: User
    profile: Profile
    illegal: Boolean
    isPublic: Boolean
    deleted: Boolean
    weight: Weight
    sport: Sport
    meal: Meal
    measurement: Measurement
    pressure: Pressure
    glucose: Glucose
    lipid: Lipid
    acid: Acid
    liked: Boolean
}
type Sport {
    id: ID
    date: DateTime
    previous: Boolean
    sportType: String
    type: String
    duration: Int
    distance: Float
    counts: Int
}
type Meal {
    id: ID
    date: DateTime
    previous: Boolean
    mealType: String
}
type Measurement {
    id: ID
    date: DateTime
    previous: Boolean
    waist: Float
    bust: Float
    hip: Float
}
type Pressure {
    id: ID
    date: DateTime
    previous: Boolean
    systolic: Int
    diastolic: Int
}
type Glucose {
    id: ID
    date: DateTime
    previous: Boolean
    glucose: Float
}
type Lipid {
    id: ID
    date: DateTime
    previous: Boolean
    cholesterol: Float
    triglyceride: Float
    hdl: Float
    ldl: Float
}
type Acid {
    id: ID
    date: DateTime
    previous: Boolean
    acid: Float
}

type SkuAttr {
    key: String
    values: [String]
    value: String
}
type ProductSku {
    id: ID
    price: Int
    image: String
    attrs: [SkuAttr]
}
type Product {
    id: ID
    name: String
    cover: String
    coverImages: [String]
    descImages: [String]
    shareImage: String
    price: Int
    originalPrice: Int
    status: Int
    needShipping: Int
    skuAttrs: [SkuAttr]
    weight: Float
    sku: [ProductSku]
}
type Package {
    id: ID
    name: String
    cover: String
    coverImages: [String]
    descImages: [String]
    shareImage: String
    price: Int
    originalPrice: Int
    products: [ProductItem]
}
""" 商品在订单中、结算中、套餐中使用 """
type ProductItem {
    id: ID
    name: String
    cover: String
    quantity: Int
    price: Int
    originalPrice: Int
    """ 暂时的，小程序更新后删除 """
    productName: String
    """ 暂时的，小程序更新后删除 """
    productCover: String
    """ 暂时的，小程序更新后删除 """
    discountPrice: Int
    totalAmount: Int
    discountAmount: Int
    finalAmount: Int
    needShipping: Boolean
}

type Address {
    id: ID
    name: String
    phone: String
    province: String
    city: String
    county: String
    town: String
    address: String
    isDefault: Boolean
}
type OrderRecipient {
    name: String
    phone: String
    province: String
    city: String
    county: String
    town: String
    address: String
}
type Order {
    id: ID
    user: User
    status: Int
    shippingStatus: Int
    totalAmount: Int
    discountAmount: Int
    finalAmount: Int
    createAt: DateTime
    updateAt: DateTime
    recipient: OrderRecipient
    prepayId: String
    needShipping: Boolean
    payTransactionId: String
    paySuccessTime: DateTime
    packageId: ID
    packageName: String
    description: String
    type: Int
    products: [ProductItem]
    shippings: [OrderShipping]
}
input OrderProductInput {
  productId: ID
  quantity: Int
}
type OrderCheckout {
    isPackage: Boolean
    totalAmount: Int
    discountAmount: Int
    finalAmount: Int
    needShipping: Boolean
    products: [ProductItem]
    package: Package,
    course: Course
}
type OrderCountInfo {
    # 等待付款
    waitingPay: Int
    # 等待发货
    waitingShip: Int
    # 等待收货
    waitingReceive: Int
}
type OrderRefund {
    id: ID
    refundNo: String
    wechatRefundId: String
    channel: String
    status: String
    userReceivedAccount: String
    amount: Int
    createAt: DateTime
    updateAt: DateTime
}
type OrderCommission {
    id: ID
    user: User
    amount: Int
    createAt: DateTime
    updateAt: DateTime
}

type OrderShipping {
  id: ID
  expressCompany: String
  expressNumber: String
  createAt: DateTime
}

type PrepayInfo {
  timeStamp: String
  nonceStr: String
  package: String
  signType: String
  paySign: String
}

input WeightInput {
    weight: Float!
    bf: Float
}
input MealInput {
    mealType: String!
}
input PressureInput {
    systolic: Int!
    diastolic: Int!
}
input GlucoseInput {
    glucose: Float!
}
input LipidInput {
    cholesterol: Float!
    triglyceride: Float!
    hdl: Float!
    ldl: Float!
}
input AcidInput {
    acid: Float!
}

input SportInput {
    sportType: String!
    duration: Int!
    distance: Float
}
input MeasurementInput {
    waist: Float
    bust: Float
    hip: Float
}
type Comment {
    id: ID
    createAt: DateTime
    status: Status
    user: User
    content: String
    replies: [Comment]
    parent: Comment
    deleted: Boolean
    illegal: Boolean
}
type PlanCountInfo {
    waitingOpen: Int
}
type Plan {
    id: ID
    status: Int
    dates: Int
    targetLose: Float
    targetWeight: Float
    startDate: DateTime
    endDate: DateTime
    startWeight: Float
    lastWeightDate: DateTime
    lastWeight: Float
    totalChanges: Float
    createAt: DateTime
    todayIndex: Int
    isShenPaybackPlan: Int
    shenPaybackEverydayAmount: Int
    shenRewardUsers: [User]
    todayWeight: PlanDateWeight
    weights: [PlanDateWeight]
}
type PlanDateWeight {
    date: DateTime
    dateIndex: Int
    weight: Float
    bf: Float
    bmi: Float
    targetWeight: Float
    targetLose: Float
    shenPaybackStatus: Int
    refundInfo: OrderRefund
    commissionInfo: OrderCommission
}
type PlanTargetInfo {
    startWeight: Float
    targetWeight: Float
    targetLose: Float
    dates: Int
    # 每天的体重目标
    targets: [Float]
}
type ShareConfig {
    title: String
    imageUrl: String
    iCanLoseImage: String
}
type MessageSubscribe {
    count: Int
    hour: Int
    frequency: String
    dayOrDate: Int
}
type Recommended {
    id: ID
    key: String
    name: String
    cover: String
    page: String
    price: Int
    desc: String
}
type ShenServicePendingPayback {
    shenServiceName: String
    pendingPaybackAmount: Int
    pendingPaybackDays: Int
}
type AppConfig {
    name: String
    value: String
}
type Course {
    id: ID
    name: String
    cover: String
    description: String
    price: Int
    status: Int
    totalSections: Int
    totalDuration: Int
    createAt: DateTime
    updateAt: DateTime
    sections: [CourseSection]

}
type  CourseSection {
    id: ID
    title: String
    description: String
    duration: Int
    status: Int
    sectionIndex: Int
    cover: String
    isFreeTrial: Boolean
    createAt: DateTime
    updateAt: DateTime
    course: Course
}
type MyCourse {
  id: ID
  purchasedAt: DateTime
  source: String
  course: Course
}

type Query {
    account: AccountWithToken
    weights(profileId: ID, dates: Int, start: QueryDate, end: QueryDate): [Weight] @auth @checkProfileId
    weightsWeek(profileId: ID): [Weight] @auth @checkProfileId
    weightsMonth(profileId: ID): WeightMonthCount @auth @checkProfileId
    measurements(profileId: ID, dates: Int, start: QueryDate, end: QueryDate): [Measurement] @auth @checkProfileId
    sports(profileId: ID, dates: Int, start: QueryDate, end: QueryDate): [Sport] @auth @checkProfileId
    pressures(profileId: ID, dates: Int, start: QueryDate, end: QueryDate): [Pressure] @auth @checkProfileId
    glucoses(profileId: ID, dates: Int, start: QueryDate, end: QueryDate): [Glucose] @auth @checkProfileId
    lipids(profileId: ID, dates: Int, start: QueryDate, end: QueryDate): [Lipid] @auth @checkProfileId
    acids(profileId: ID, dates: Int, start: QueryDate, end: QueryDate): [Acid] @auth @checkProfileId
    status(userId: ID, month: String, profileId: ID, lastId: ID, start: QueryDate, end: QueryDate, type: String): [Status] @auth
    singleStatus(id: ID!): Status @auth
    recommendedStatus(lastId: ID): [Status] @auth
    comments(statusId: ID, lastId: ID): [Comment] @auth
    user(id: ID!): User
    myLikes(lastId: ID): [Like] @auth
    myComments(lastId: ID): [MyComment] @auth
    myNotifications(category: String, lastId: ID): [Notification] @auth
    myUnreadNotificationsCount: Int @auth
    myAddresses: [Address] @auth
    myDefaultAddress: Address @auth
    # products: [Product]
    recommended: [Recommended]
    recommendation(category: String!): [Recommended]
    product(id: ID!): Product
    package(id: ID!): Package
    """ 获取订单结算信息 """
    orderCheckout(products: [OrderProductInput], packageId: ID, courseId: ID): OrderCheckout @auth
    order(id: ID!): Order @auth
    orders(status: Int, shippingStatus: Int, lastId: ID): [Order] @auth
    ordersCountInfo: OrderCountInfo @auth
    plans(status: Int, lastId: ID): [Plan] @auth
    plansCountInfo: PlanCountInfo @auth
    plan(id: ID!): Plan @auth
    """ 获取当前用户正在进行的计划或者等待开启的计划 """
    progressingPlan(profileId: ID): Plan @auth
    planTarget(profileId: ID!, weight: Float!, dates: Int!): PlanTargetInfo @auth(profiled: true)
    """ 计算当前用户可以减重多少 """
    howMuchCanILose(profileId: ID, dates: Int = 28): Float
    howMuchCanILoses(profileId: ID): [[Float]]
    """ 沈老师要的减重后的血压等的改变 """
    benefitAfterLose(profileId: ID): [Float]
    qiniuToken(prefix: String!): String @auth
    shareConfig: ShareConfig
    messageSubscribe: MessageSubscribe @auth
    # inShenService: Boolean @auth
    shenServicePendingPayback: ShenServicePendingPayback @auth
    mpReferralQrCode: String @auth
    courses: [Course]
    course(id: ID!): Course
    myCourses: [MyCourse]
}
type Mutation {
    sendSmsCode(mobile: String!): Boolean
    """ Registers a new user, binds a mobile number, and associates a referrer if provided """
    register(referrerID: ID, mobileCode: String!): Account @auth @refreshCache(returnAccount: true)
    """ 完善资料 """
    completeProfile(name: String!, avatar: String!, gender: Int!, birth: String!, height: Float!, weight: Float, target: Float): Account @auth @refreshCache(returnAccount: true)
    """ 编辑或添加档案 """
    saveProfile(id: ID, name: String, avatar: Image, gender: Int, birth: String, height: Float, target: Float): Account @auth(profiled: true) @refreshCache(returnAccount: true)
    """ 删除档案 """
    deleteProfile(id: ID!): Account @auth @refreshCache(returnAccount: true)
    """ 发表打卡 """
    createStatus(profileId: ID, previous: Boolean, date: String, type: StatusType!, content: String = "", isPublic: Boolean = false, subscribe: Boolean = false, images: [String], weight: WeightInput, meal: MealInput, sport: SportInput, measurement: MeasurementInput, pressure: PressureInput, glucose: GlucoseInput, lipid: LipidInput, acid: AcidInput): Account @auth(profiled: true) @refreshCache(returnAccount: true)
    changeStatusPublic(id: ID!, isPublic: Boolean!): Boolean @auth
    """ 删除打卡 """
    deleteStatus(id: ID!): Account @auth @refreshCache(returnAccount: true)
    """ 关注用户 """
    follow(id: ID!): Boolean @auth
    """ 取消关注用户 """
    unFollow(id: ID!): Boolean @auth
    """ 点赞内容 """
    like(entityId: ID!, entityType: EntityType!): Boolean @auth
    """ 取消点赞 """
    unLike(entityId: ID!, entityType: EntityType!): Boolean @auth
    """ 发表浏览 """
    comment(statusId: ID!, parentId: ID, content: String!): Boolean @auth
    """ 删除评论 """
    deleteComment(id: ID): Boolean @auth
    readNotification(maxId: ID): Boolean @auth
    saveAddress(id: ID, name: String!, phone: String!, province: String!, city: String!, county: String!, address: String!, isDefault: Boolean): Address @auth
    deleteAddress(id: ID!): Boolean @auth
    """ 创建订单- 沈老师 自助减重订单 需要验证小红书或抖音的订单号 """
    createShenPaybackPlanOrderByOuter(outerOrderNumber: String!): PrepayInfo @auth
    """ 创建订单 """
    createOrder(products: [OrderProductInput], packageId: ID, addressId: ID, courseId: ID): PrepayInfo @auth
    """ 为下一步微信支付做准备 """
    prepayOrder(orderId: ID!): PrepayInfo @auth
    """ 删除订单 """
    deleteOrder(id: ID): Boolean @auth
    """ 确认收货 """
    completeOrder(id: ID): Boolean @auth
    """ 开启计划 """
    startPlan(id: ID!, profileId: ID, startWeight: Float!, startDate: String): Boolean @auth(profiled: true)
    changePlanWeight(id: ID!, weight: Float!): Boolean @auth(profiled: true)
    planPayback(planId: ID!, date: String!) : Boolean @auth
    planReward(planId: ID!, date: String!, userId: ID!): Boolean @auth
    messageSubscribe(hour: Int = null, frequency: String = null, dayOrDate: Int = null): Boolean @auth
    """ 沈老师教育服务返现，对当前所有可以退款的天数进行操作， action 2 退款 , 3 奖励 """
    shenServicePayback(action: Int): Boolean

}
