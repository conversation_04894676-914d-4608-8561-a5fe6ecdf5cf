import DataLoader from 'dataloader'
import _ from 'lodash-es'
import config from 'config'
import kn from './kn.js'
const illegalImg = config.get('illegalImg')
const imgHost = config.get('imgHost')

function genLoader (tableName, columnName = 'id', columns = null) {
  return new DataLoader(async function (ids) {
    const qb = kn(tableName).whereIn(columnName, ids)
    const list = columns ? await qb.select(columns) : await qb.select()
    const indexed = _.keyBy(list, columnName)
    return ids.map(i => indexed[i] || null)
  })
}
export default function create (account) {
  return {
    userLoader: genLoader('user', 'id', ['id', 'name', 'gender', 'avatar', 'referrerUserId']),
    profileLoader: genLoader('user_profile', 'id', ['id', 'self', 'name', 'avatar', 'gender']),
    statusLoader: genLoader('status'),
    statusImagesLoader: new DataLoader(async function (statusIds) {
      const list = await kn.table('status_image').whereIn('statusId', statusIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.statusId] = memo[item.statusId] || []
        let img = item.illegal ? illegalImg : item.path
        if (!item.path.startsWith('http')) img = imgHost + img
        memo[item.statusId].push(img)
        return memo
      }, {})
      return statusIds.map(i => grouped[i])
    }),
    statusWeightLoader: genLoader('status_weight', 'statusId'),
    statusSportLoader: genLoader('status_sport', 'statusId'),
    statusMealLoader: genLoader('status_meal', 'statusId'),
    statusMeasurementLoader: genLoader('status_measurement', 'statusId'),
    statusPressureLoader: genLoader('status_pressure', 'statusId'),
    statusGlucoseLoader: genLoader('status_glucose', 'statusId'),
    statusLipidLoader: genLoader('status_lipid', 'statusId'),
    statusAcidLoader: genLoader('status_acid', 'statusId'),
    statusLikeLoader: new DataLoader(async function (statusIds) {
      if (!account) return statusIds.map(() => false)
      const list = await kn.table('like').where({ entityType: 'status', userId: account.id, deleted: 0 }).whereIn('entityId', statusIds).select('entityId')
      const indexed = _.keyBy(list, 'entityId')
      return statusIds.map(i => !!indexed[i])
    }),
    commentLoader: new DataLoader(async function (ids) {
      let list = await kn.table('comment').whereIn('id', ids).select()
      list = list.map(i => i.deleted ? { ...i, content: '已删除' } : i.illegal ? { ...i, content: '内容违规' } : i)
      const indexed = _.keyBy(list, 'id')
      return ids.map(i => indexed[i])
    }),
    commentLikeLoader: new DataLoader(async function (statusIds) {
      if (!account) return statusIds.map(() => false)
      const list = await kn.table('like').where({ entityType: 'comment', userId: account.id, deleted: 0 }).whereIn('entityId', statusIds).select('entityId')
      const indexed = _.keyBy(list, 'entityId')
      return statusIds.map(i => !!indexed[i])
    }),
    userFollowedLoader: new DataLoader(async function (userIds) {
      // 0 未关注 1 关注 2 相互关注 3 他关注我
      if (!account) return userIds.map(() => 0)
      const list = await kn('user_follower').where(qb => {
        qb.where('followerUserId', account.id).whereIn('userId', userIds)
      }).orWhere(qb => {
        qb.where('userId', account.id).whereIn('followerUserId', userIds)
      }).select('id', 'userId', 'followerUserId')
      const indexed = _.reduce(list, (memo, item) => {
        return !memo[item.followerUserId]
          ? { [item.followerUserId]: { [item.userId]: true } }
          : { ...memo, [item.followerUserId]: { ...memo[item.followerUserId], [item.userId]: true } }
      }, {})
      return userIds.map(id => {
        const following = indexed[account.id] && indexed[account.id][id]
        const followedBy = indexed[id] && indexed[id][account.id]
        return following && followedBy ? 2 : following ? 1 : followedBy ? 3 : 0
      })
    }),
    likeLoader: genLoader('like'),
    orderLoader: genLoader('order'),
    orderProductsLoader: new DataLoader(async function (orderIds) {
      const list = await kn.table('order_product').whereIn('orderId', orderIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.orderId] = memo[item.orderId] || []
        memo[item.orderId].push(item)
        return memo
      }, {})
      return orderIds.map(i => grouped[i])
    }),
    orderRefundLoader: genLoader('order_refund'),
    orderCommissionLoader: genLoader('order_commission'),
    orderShippingLoader: new DataLoader(async function (orderIds) {
      const list = await kn.table('order_shipping').whereIn('orderId', orderIds).select()
      const grouped = _.reduce(list, (memo, item) => {
        memo[item.orderId] = memo[item.orderId] || []
        memo[item.orderId].push(item)
        return memo
      }, {})
      return orderIds.map(i => grouped[i])
    }),
    // 为了在不同的 query 不重复查询而建立的 loader
    progressingPlanLoader: new DataLoader(async function (profileIds) {
      const results = []
      for (const profileId of profileIds) {
        if (!account) return null
        const item = await kn.table('plan').where({ userId: account.id, profileId, status: 1 }).first()
        if (item) {
          results.push(item)
        } else {
          const item2 = await kn.table('plan').where({ userId: account.id, status: 0 }).orderBy('id', 'asc').first()
          results.push(item2 ?? null)
        }
      }
      return results
    }),
    packageLoader: new DataLoader(async function (packageIds) {
      const list = await kn.table('package').join('package_app', 'package.id', 'package_app.packageId').whereIn('package.id', packageIds).select('package.*', 'package_app.app')
      const products = await kn('package_product')
        .join('product', 'package_product.productId', 'product.id')
        .whereIn('package_product.packageId', packageIds)
        .where({ 'product.status': 1 })
        .select('package_product.packageId', 'product.id', 'product.name', 'package_product.price', 'package_product.quantity', 'product.price as originalPrice', 'product.cover')
      const grouped = _.groupBy(products, 'packageId')
      const indexed = _.keyBy(list, 'id')
      return packageIds.map(i => {
        const pack = indexed[i]
        if (pack) {
          pack.products = grouped[i] || []
        }
        return pack || null
      })
    }),
    productLoader: new DataLoader(async function (productIds) {
      // const list = await kn.table('product').join('product_app', 'product.id', 'product_app.productId').whereIn('product.id', productIds).select('product.*', 'product_app.app')
      const list = await kn.table('product').whereIn('id', productIds).select()
      const apps = await kn('product_app').whereIn('productId', productIds).select()
      const appsIndexed = apps.reduce((memo, i) => {
        memo[i.productId] = memo[i.productId] || []
        memo[i.productId].push(i.app)
        return memo
      }, {})
      const indexed = list.reduce((memo, i) => ({ [i.id]: { ...i, apps: appsIndexed[i.id] || [], ...memo } }), {})
      return productIds.map(i => indexed[i] || null)
    }),
    addressLoader: genLoader('address'),
    courseLoader: genLoader('course'),
    courseSectionsLoader: new DataLoader(async function (courseIds) {
      const list = await kn.table('course_section').whereIn('courseId', courseIds).select()
      const grouped = _.groupBy(list, 'courseId')
      return courseIds.map(i => grouped[i] || [])
    })
  }
}
