import { v4 as uuidv4 } from 'uuid'
import kn from '../db/kn.js'
import redis from '../db/redis.js'
import createHttpError from 'http-errors'

async function getUser (id) {
  const user = await redis.get(`account:${id}`)
  if (user) return JSON.parse(user)
  return reSaveAccount(id)
}

export async function reSaveAccount (id) {
  const user = await kn('user').where({ id }).first(['id', 'app', 'openId', 'unionId', 'gender', 'name', 'avatar', 'mobile'])
  if (!user) throw createHttpError(500, '没有对应的用户')
  const profiles = await kn('user_profile').where({ userId: id, deleted: 0 }).orderBy('id', 'asc').select('id', 'self', 'name', 'avatar', 'gender', 'birth', 'height', 'heightAt', 'target', 'targetAt', 'weight', 'bmi', 'bf', 'weightAt')
  const u = { ...user, profiles }
  await redis.set(`account:${id}`, JSON.stringify(u), 'EX', 60 * 60 * 24 * 7)
  return u
}

export async function getAccountTokenById (id) {
  const user = await getUser(id)
  const token = uuidv4().replace(/-/g, '')
  await redis.set(`account_id_token:${id}`, token)
  await redis.set(`account_token_id:${token}`, id)
  return { token, user }
}
/**
 * Retrieves the currently logged-in user based on the provided token.
 *
 * @param {string} token - The token associated with the user session.
 * @returns {Promise<object|null>} The user object if the token is valid, otherwise null.
 */
export async function getAccountByToken (token) {
  const id = await redis.get(`account_token_id:${token}`)
  if (!id) return null
  return await getUser(id)
}

/**
 * Creates a new account with the provided information.
 *
 * @param {object} info - The information required to create a new account, which includes:
 * @param {string} info.app - The application identifier.
 * @param {string} info.openId - The OpenID from the OAuth provider.
 * @param {string} info.unionId - The UnionID that uniquely identifies the user across multiple apps from the same provider.
 * @param {string} [info.referrerUserId] - The user ID of the referrer (optional).
 * @returns {Promise<{token: string, user: object}>} An object containing the token and user details.
 */
export async function createAccount (info) {
  const id = await kn.transaction(async (trx) => {
    const u = await trx('user').where({ app: info.app, openId: info.openId }).first('id')
    if (u) return u.id
    const [userId] = await trx('user').insert(info)
    await trx('user_profile').insert({ userId, self: true })
    if (info.referrerUserId) {
      await trx('user_recommendation').insert({ referredUserId: userId, referrerUserId: info.referrerUserId })
    }
    return userId
  })
  const token = uuidv4().replace(/-/g, '')
  await redis.set(`account_id_token:${id}`, token)
  await redis.set(`account_token_id:${token}`, id.toString())
  const user = await reSaveAccount(id)
  return { token, user }
}
