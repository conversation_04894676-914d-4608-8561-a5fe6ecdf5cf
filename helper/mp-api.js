import axios from 'axios'
import createHttpError from 'http-errors'
import config from 'config'
import redis from '../db/redis.js'

const labels = {
  100: '正常',
  10001: '广告',
  20001: '时政',
  20002: '色情',
  20003: '辱骂',
  20006: '违法犯罪',
  20008: '欺诈',
  20012: '低俗',
  20013: '版权',
  21000: '其他'
}

const mps = config.get('mp')

class MPApi {
  constructor (mp) {
    if (!mps[mp]) throw createHttpError(500, '没有对应的小程序')
    const { appID, appSecret } = mps[mp]
    this.app = mp
    this.appID = appID
    this.appSecret = appSecret
    this.instance = axios.create({
      timeout: 30 * 1000,
      validateStatus: (status) => status === 200
    })
  }

  async fetchApi (options) {
    const res = await this.instance(options)
    if (res.status !== 200) {
      console.error(res.status)
      console.error(res.data)
      throw createHttpError(500, '微信服务返回错误')
    }
    if (res.data.errcode) {
      console.error('mp error code: ' + res.data.errcode)
      console.error('mp error message: ' + res.data.errmsg)
      throw createHttpError(500, '微信服务:' + res.errmsg)
    }
    return res.data
  }

  async getToken () {
    const token = await redis.get('access_token:' + this.app)
    if (token) return token
    const { access_token: accessToken, expires_in: expiresIn } = await this.fetchApi({ method: 'post', url: 'https://api.weixin.qq.com/cgi-bin/stable_token', data: { appid: this.appID, secret: this.appSecret, grant_type: 'client_credential' } })
    redis.set('access_token:' + this.app, accessToken, 'EX', expiresIn)
    return accessToken
  }

  async code2session (code) {
    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${this.appID}&secret=${this.appSecret}&js_code=${code}&grant_type=authorization_code`
    const res = await this.fetchApi(url)
    const { unionid: unionId, openid: openId, session_key: sessionKey } = res
    return { openId, unionId, sessionKey }
  }

  async code2mobile (code) {
    const token = await this.getToken()
    const { phone_info: { phoneNumber, countryCode } } = await this.fetchApi({ method: 'post', url: `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${token}`, data: { code } })
    return { mobile: phoneNumber, mobileCountry: countryCode }
  }

  async checkTextRisky (info) {
    const { scene, openId, content, title } = info
    console.log(info)
    const data = {
      version: 2,
      scene: scene || 4,
      openid: openId,
      content,
      title
    }
    const token = await this.getToken()
    const { result: { suggest, label } } = await this.fetchApi({ method: 'post', url: `https://api.weixin.qq.com/wxa/msg_sec_check?access_token=${token}`, data })
    if (suggest === 'risky') {
      console.error(`text risky, ${label},  content: ${content}`)
      return `内容审核不通过，包含${labels[label]}信息`
    }
    return null
  }

  async getMiniBarCodeUnlimited (options) {
    const token = await this.getToken()

    const res = await this.instance({
      method: 'post',
      url: `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${token}`,
      responseType: 'arraybuffer',
      data: options
    })
    if (res.status !== 200) {
      console.error(res.status)
      console.error(res.data)
      throw createHttpError(500, '微信服务返回错误')
    }
    const contentType = res.headers['content-type']
    if (contentType.indexOf('image') === -1) {
      const jsonStr = Buffer.from(res.data, 'binary').toString('utf-8')
      const errorJson = JSON.parse(jsonStr)
      // return errorJson
      console.log('----', errorJson)
      throw createHttpError(400, '请求qr参数错误')
    } else {
      const buffer = Buffer.from(res.data, 'binary')
      return buffer
    }
  }
}

export default MPApi
