import { round } from 'mathjs'

export default function (dates, height, weight) {
  height = height / 100
  const targets = []
  const t28 = function (weight) {
    const BMI = weight / (height * height)
    const target28 = (0.1245 * BMI - 1.7299) * height * height * 2 * (dates > 28 ? 0.854 : 1)
    // 当月减重量（斤）Y=(0.1245*BMI -1.7299)*身高（米）*身高（米）*2
    // Y=当月减重量（斤）
    // 第几天减重= Y*(-0.2336*第几天+6.954)/100   （斤）
    let allTw = 0
    for (let i = 1; i <= 28; i++) {
      const tw = target28 * (-0.2336 * i + 6.954) / 100
      allTw = allTw + tw
      targets.push({
        // date: moment(start).add(i, 'days').format('YYYY-MM-DD'),
        targetWeight: round(weight - allTw / 2, 2),
        targetLose: round(tw / 2, 2)
      })
    }
  }
  t28(weight)
  if (dates >= 56) {
    const { targetWeight } = targets[targets.length - 1]
    t28(targetWeight)
  }
  if (dates >= 84) {
    const { targetWeight } = targets[targets.length - 1]
    t28(targetWeight)
  }
  return targets
}
