import axios from 'axios'
import config from 'config'
import redis from '../db/redis.js'
const { agentId, corpId, appSecret } = config.get('qywx')

async function getToken () {
  const k = 'qywx:access_token:app'
  const token = await redis.get(k)
  if (token) return token
  const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpId}&corpsecret=${appSecret}`
  const res = await axios.request({ url, timeout: 30000 })
  if (res.data.errcode) {
    throw new Error(res.data.errmsg)
  }
  const token2 = res.data.access_token
  await redis.set(k, token2, 'EX', res.data.expires_in - 20)
  return token2
}

export async function sendText (content, toUser) {
  try {
    const token = await getToken()
    const url = `https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=${token}`
    const data = {
      touser: toUser,
      msgtype: 'text',
      agentid: agentId,
      text: { content }
    }
    const res = await axios.request({ url, data, timeout: 30000, method: 'POST' })
    console.log(res?.data)
    if (res.data.errcode) {
      console.log(res.data)
      throw new Error(res.data.errmsg)
    }
  } catch (error) {
    console.error(error)
    throw error
  }
}
