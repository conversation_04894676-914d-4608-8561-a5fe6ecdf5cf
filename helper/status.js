import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween.js'
import objectSupport from 'dayjs/plugin/objectSupport.js'
import _ from 'lodash-es'
import kn from '../db/kn.js'
// import redis from '../db/redis.js'
import { GraphQLError } from 'graphql'
import httpErrorCode from '../util/http-error-code.js'

dayjs.extend(isBetween)
dayjs.extend(objectSupport)

/**
 * Posts a new status update for a user.
 * @param {string} app - The application context.
 * @param {Object} account - The user account object.
 * @param {Object} restArgs - The data object for the new status, which includes:
 * @param {boolean} restArgs.subscribe - Whether the user is subscribing.
 * @param {boolean} restArgs.previous - Whether this is a previous status.
 * @param {number} restArgs.profileId - The profile ID associated with the status.
 * @param {string} restArgs.date - The date of the status.
 * @param {string} restArgs.content - The content of the status.
 * @param {Array<string>} restArgs.images - The images associated with the status.
 * @param {string} restArgs.type - The type of the status (e.g., weight, meal, sport, measurement).
 */
export async function createStatus (app, account, data) {
  let { previous, profileId, date, content, images, type, isPublic, ...extra } = data
  if (!profileId) profileId = account.profiles[0].id
  const profile = await kn('user_profile').where({ id: profileId }).first()
  if (!profile) throw new GraphQLError('用户档案没有找到', { extensions: { code: httpErrorCode.NOT_FOUND } })
  const { weight, meal, sport, measurement } = extra
  // if (subscribe) {
  //   await redis.lpush(`subscribe:${account.id}|${account.openId}`, Date.now())
  // }
  // 验证输入
  if (content && content.length > 5000) throw new GraphQLError('内容长度不能超过5000字', { extensions: { code: httpErrorCode.BAD_REQUEST } })
  if (images && images.length > 0 && _.some(images, i => i.indexOf('status/') !== 0)) throw new GraphQLError('图片路径错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
  switch (type) {
    case 'weight':
      if (!weight || weight.weight < 8 || weight.weight > 220) throw new GraphQLError('体重输入错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      break
    case 'meal':
      if (!meal?.mealType) throw new GraphQLError('餐饮输入错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      break
    case 'sport':
      if (!sport?.sportType || !sport?.duration || sport.duration < 0) throw new GraphQLError('运动输入错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      break
    case 'measurement':
      if (!measurement?.waist && !measurement?.hip) throw new GraphQLError('测量输入错误', { extensions: { code: httpErrorCode.BAD_REQUEST } })
      break
    default:
      break
  }
  const dateAt = dayjs(previous ? date : new Date()).format('YYYY-MM-DD HH:mm:ss')
  await kn.transaction(async (trx) => {
    const [statusId] = await trx('status').insert({ app, content, userId: account.id, profileId, type, isPublic })
    if (images && images.length > 0) {
      await trx('status_image').insert(images.map(i => ({ statusId, path: i, userId: account.id })))
    }
    // 浙大沈教授“HEALTH积优生活”教育
    if (app === 'shen' && (!previous || dayjs(dateAt).isSame(dayjs(), 'day'))) {
      const today = dayjs(dateAt).format('YYYY-MM-DD')
      const shenService = await trx('shen_service').where({ userId: account.id }).where('start', '<=', today).where('end', '>=', today).first()
      if (shenService) {
        await trx('shen_service_day').where({ shenServiceId: shenService.id, date: today, status: 0 }).update({ status: 1 })
      }
    }
    if (type === 'status') return
    // 表 status_[type] 的插入字段
    let typeParams = { ...extra[type] }
    if (type === 'weight') {
      const bmi = weight.weight * 100 * 100 / (profile.height * profile.height)
      typeParams = { ...typeParams, bmi }
    }
    await trx(`status_${type}`).insert({ ...typeParams, statusId, date: dateAt, previous: !!previous })
    if (type === 'weight') {
      // 更新 day_weight 表
      let dayInfo = { ...typeParams, date: dateAt, userId: account.id, profileId: profile.id }
      let values = [typeParams]
      if (previous) {
        // const weights = await trx('status_weight').where({ deleted})
        const previousWeight = await trx('status_weight')
          .join('status', 'status.id', 'status_weight.statusId')
          .where({
            'status.type': 'weight',
            'status.userId': account.id,
            'status.profileId': profile.id,
            'status.deleted': false
          })
          .andWhere('date', '>=', dayjs(dateAt).format('YYYY-MM-DD'))
          .andWhere('date', '<', dayjs(dateAt).add(1, 'day').format('YYYY-MM-DD'))
          .orderBy('createAt', 'desc')
          .first()
        const info2 = _.pick(previousWeight, ['weight', 'bmi', 'bf'])
        dayInfo = { ...info2, date: dateAt, userId: account.id, profileId: profile.id }
        values = [info2]
      }
      await trx.raw(kn('day_weight').insert(dayInfo).toString() + ' on duplicate key update ?', values)
      // 更新 plan 表 和 user_profile 表 的最新体重
      if (!previous || dayjs(dateAt).isSame(profile.weightAt, 'day')) {
        await trx('user_profile').where({ id: profile.id }).update({ ...typeParams, weightAt: dateAt })
        const plan = await trx('plan').where({ userId: account.id, profileId: profile.id, status: 1 }).first()
        if (plan && dayjs(dateAt).isBetween(dayjs(plan.startDate), dayjs(plan.endDate), 'day', '[]')) {
          const updatePlanInfo = { lastWeightDate: dayjs(dateAt).format('YYYY-MM-DD HH:mm:ss'), lastWeight: typeParams.weight, totalChanges: typeParams.weight - plan.startWeight }
          await trx('plan').where({ id: plan.id }).update(updatePlanInfo)
          // 沈老师小程序返现计划
          if (plan.isShenPaybackPlan) {
            await trx('plan_target').where({ planId: plan.id, date: dayjs(dateAt).format('YYYY-MM-DD'), shenPaybackStatus: 0 }).update({ shenPaybackStatus: 1 })
          }
        }
      }
    }
  })
}

/**
 *
 * @param {string} app
 * @param {Object} account
 * @param {number} id
 * @returns
 */
export async function removeStatus (app, account, id) {
  const status = await kn('status').where({ id }).first()
  const profile = await kn('user_profile').where({ id: status.profileId }).first()
  if (!status || status.userId !== account.id) throw new GraphQLError('内容没有找到', { extensions: { code: httpErrorCode.NOT_FOUND } })
  if (status.deleted) return true
  await kn.transaction(async function (trx) {
    await trx('status').where({ id }).update({ deleted: true, deleteAt: kn.raw('current_timestamp') })
    if (status.type !== 'status') await trx(`status_${status.type}`).where({ statusId: id }).update({ deleted: true })
    if (status.type !== 'weight') return
    // 更新 weight 相关表
    const statusWeight = await trx('status_weight').where({ statusId: id }).first()
    // 删除体重的日期
    const date = dayjs(statusWeight.date).format('YYYY-MM-DD')
    // 查找当天其他体重
    const dayOtherWeight = await trx('status_weight')
      .join('status', 'status.id', 'status_weight.statusId')
      .where({
        'status.type': 'weight',
        'status.userId': account.id,
        'status.profileId': profile.id,
        'status.deleted': false
      })
      .andWhere('date', '>=', date)
      .andWhere('date', '<', dayjs(statusWeight.date).add(1, 'day').format('YYYY-MM-DD'))
      .andWhereNot('status_weight.statusId', statusWeight.statusId)
      .orderBy('date', 'desc')
      .first()
    if (dayOtherWeight) {
      await trx('day_weight').where({ userId: account.id, profileId: profile.id, date }).update(_.pick(dayOtherWeight, ['weight', 'bmi', 'bf']))
    } else {
      await trx('day_weight').where({ userId: account.id, profileId: profile.id, date }).delete()
    }
    const lastDayWeight = await trx('day_weight').where({ userId: account.id, profileId: profile.id }).orderBy('date', 'desc').first()
    if (lastDayWeight) {
      await trx('user_profile').update({
        weight: lastDayWeight.weight,
        bmi: lastDayWeight.bmi,
        bf: lastDayWeight.bf,
        weightAt: lastDayWeight.date
      }).where({ id: profile.id })
    }
    // 更新 plan 表
    const plan = await trx('plan').where({ userId: account.id, profileId: profile.id, status: 1 }).first()
    if (plan) {
      const lastPlanWeight = await kn('day_weight').where({ userId: account.id, profileId: profile.id })
        .where('date', '<=', dayjs(plan.endDate).format('YYYY-MM-DD'))
        .where('date', '>=', dayjs(plan.startDate).format('YYYY-MM-DD')).orderBy('date', 'desc').first()
      if (lastPlanWeight) {
        await trx('plan').where({ id: plan.id }).update({ lastWeightDate: lastPlanWeight.date, lastWeight: lastPlanWeight.weight, totalChanges: lastPlanWeight.weight - plan.startWeight })
      }
    }
  })
  return true
}
