import config from 'config'
import qn from 'qiniu'
import { hashID } from '../util/hashids.js'
const { accessKey, secretKey, scope } = config.get('qiniu')
const mac = new qn.auth.digest.Mac(accessKey, secretKey)
// const production = process.env.NODE_ENV === 'production'

export function getUploadToken (userId, prefix) {
  // expires 单位为秒，为上传凭证的有效时间
  const options = {
    scope,
    expires: 1800,
    forceSaveKey: true,
    saveKey: `${prefix}/${hashID.encode(userId)}/$(etag)`, // prefix + '/$(etag)',
    mimeLimit: 'image/*',
    returnBody: '{"key": $(key),"hash": $(etag), "fsize":$(fsize), "imageAve": $(imageAve)}'
  }
  // if (production) {
  //   options.callbackUrl = callbackUrl
  //   options.callbackBody = '{"key": $(key), "hash": $(etag), "fsize": $(fsize), "user": $(endUser), "mimeType": $(mimeType), "imageAve": $(imageAve), "imageInfo": $(imageInfo)}'
  //   options.callbackBodyType = 'application/json'
  // }
  const putPolicy = new qn.rs.PutPolicy(options)
  return putPolicy.uploadToken(mac)
}

export function isQiniuCallback (req) {
  const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`
  return qn.util.isQiniuCallback(mac, url, null, req.get('Authorization'))
}

export async function checkFileExists (key) {
  const mac = new qn.auth.digest.Mac(accessKey, secretKey)
  const config = new qn.conf.Config()
  config.useHttpsDomain = true
  config.regionsProvider = qn.httpc.Region.fromRegionId('z0')
  const bucketManager = new qn.rs.BucketManager(mac, config)
  const result = await bucketManager
    .stat(scope, key)
  return result.resp.statusCode === 200
}

export async function uploadImage (key, buffer) {
  const options = {
    scope,
    expires: 1800,
    forceSaveKey: true,
    saveKey: key,
    mimeLimit: 'image/*',
    returnBody: '{"key": $(key),"hash": $(etag), "fsize":$(fsize), "imageAve": $(imageAve)}'
  }
  const putPolicy = new qn.rs.PutPolicy(options)
  const token = putPolicy.uploadToken(mac)
  const config = new qn.conf.Config()
  config.regionsProvider = qn.httpc.Region.fromRegionId('z0')
  const putExtra = new qn.form_up.PutExtra()
  const formUploader = new qn.form_up.FormUploader(config)
  const result = await formUploader
    .putStream(token, options.saveKey, buffer, putExtra)
  const { data, resp } = result
  if (resp.statusCode === 200) {
    return data.key
  } else {
    throw new Error('upload failed')
  }
}
