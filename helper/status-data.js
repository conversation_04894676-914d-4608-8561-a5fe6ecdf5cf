import dayjs from 'dayjs'
import kn from '../db/kn.js'

async function getStatusData (statusType, userId, profileId, dates, start, end) {
  const qb = kn('status').join(`status_${statusType}`, 'status.id', 'statusId').where('type', statusType).where('userId', userId).where('profileId', profileId)
    .where('status.deleted', 0).orderBy('date', 'desc')
  if (dates && dates <= 1000) {
    qb.where('date', '>=', dayjs().subtract(dates, 'days').format('YYYY-MM-DD'))
  } else if (start && end) {
    if (start) qb.where('date', '>=', start)
    if (end) qb.where('date', '<', dayjs(end).add(1, 'day').format('YYYY-MM-DD'))
  } else {
    qb.limit(100)
  }
  const list = await qb.select()
  return list.reverse()
}

export const sports = async (userId, profileId, dates, start, end) => {
  const qbTemp = kn('status').join('status_sport', 'status.id', 'status_sport.statusId').where('status.type', 'sport').where('status.userId', userId).where('status.profileId', profileId)
    .where('status.deleted', 0)
  const qb = qbTemp.clone().orderBy('date', 'desc')
  if (dates && dates <= 1000) {
    qb.where('date', '>=', dayjs().subtract(dates, 'days').format('YYYY-MM-DD'))
  } else if (start && end) {
    if (start) qb.where('date', '>=', start)
    if (end) qb.where('date', '<', dayjs(end).add(1, 'day').format('YYYY-MM-DD'))
  } else {
    qb.limit(100)
  }
  console.log(qb.toString())
  const list = await qb.select()
  if (list.length > 0 && (!start || !end)) {
    const lastDate = dayjs(list[list.length - 1].date).format('YYYY-MM-DD')
    const lasts = await qbTemp.clone().where('date', '>=', dayjs(lastDate).format('YYYY-MM-DD')).where('date', '<', dayjs(lastDate).add(1, 'day').format('YYYY-MM-DD')).orderBy('date', 'asc').select()
    for (const item of lasts) {
      if (!list.some(i => i.id === item.id)) list.push(item)
    }
    const firstDate = dayjs(list[0].date).format('YYYY-MM-DD')
    const firsts = await qbTemp.clone().where('date', '>=', firstDate).where('date', '<=', firstDate).orderBy('date', 'desc').select()
    for (const item of firsts) {
      if (!list.some(i => i.id === item.id)) list.unshift(item)
    }
  }
  const final = list.reduce((memo, item) => {
    const { date, duration } = item
    const d = dayjs(date).hour(0).minute(0).second(0).millisecond(0).valueOf()
    if (!memo.length || memo[0].date !== d) {
      memo.unshift({ date: d, duration })
    } else {
      memo[0].duration += duration
    }
    return memo
  }, [])
  return final
}
export const pressures = (userId, profileId, dates, start, end) => getStatusData('pressure', userId, profileId, dates, start, end)

export const measurements = (userId, profileId, dates, start, end) => getStatusData('measurement', userId, profileId, dates, start, end)

export const glucoses = (userId, profileId, dates, start, end) => getStatusData('glucose', userId, profileId, dates, start, end)

export const lipids = (userId, profileId, dates, start, end) => getStatusData('lipid', userId, profileId, dates, start, end)

export const acids = (userId, profileId, dates, start, end) => getStatusData('acid', userId, profileId, dates, start, end)
