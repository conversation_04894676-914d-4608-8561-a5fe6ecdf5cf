{"name": "tqq-mp-api", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "pm2 start pm2.config.cjs", "deploy-production-setup": "pm2 deploy pm2.config.cjs production setup", "deploy-production": "pm2 deploy pm2.config.cjs production", "deploy-dev-setup": "pm2 deploy pm2.config.cjs dev setup", "deploy-dev": "pm2 deploy pm2.config.cjs dev", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "graphql": {"schema": "./graphql/schema.graphql", "documents": "**/*.{graphql,js,ts,jsx,tsx}"}, "dependencies": {"@apollo/server": "^4.10.4", "axios": "^1.7.2", "config": "^3.3.11", "dataloader": "^2.2.2", "dayjs": "^1.11.11", "express": "^4.19.2", "graphql": "^16.8.1", "hashids": "^2.3.0", "http-errors": "^2.0.0", "ioredis": "^5.4.1", "knex": "^3.1.0", "lodash-es": "^4.17.21", "mathjs": "^13.0.3", "mysql2": "^3.10.0", "qiniu": "^7.12.0", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/js": "^9.4.0", "eslint": "^9.4.0", "globals": "^15.4.0"}}