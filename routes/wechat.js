import { Router } from 'express'
import config from 'config'
import crypto from 'crypto'
import dayjs from 'dayjs'
import kn from '../db/kn.js'
import WXMsgCrypto from '../helper/wxmsg-crypto.js'

const router = Router()
const mp = config.get('mp')

router.get('/callback/:app', async (req, res) => {
  try {
    const appConfig = mp[req.params.app]
    if (!appConfig) return res.status(404).end()
    const { signature, timestamp, nonce, echostr } = req.query
    const { callbackToken } = appConfig
    const arr = [callbackToken, timestamp, nonce]
    const check = crypto.createHash('sha1').update(arr.sort().join('')).digest('hex')
    res.send(check === signature ? echostr : 'fail')
  } catch (error) {
    res.send('fail')
  }
})

router.post('/callback/:app', async (req, res) => {
  // https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/subscribe-message.html#%E8%AE%A2%E9%98%85%E6%B6%88%E6%81%AF%E4%BA%8B%E4%BB%B6%E6%8E%A8%E9%80%81
  try {
    if (!mp[req.params.app]) return res.status(404).end()
    const { appID, callbackToken, callbackAesKey } = mp.get(req.params.app)
    const { timestamp, nonce, msg_signature: msgSignature } = req.query
    if (!req.body.Encrypt && !msgSignature) {
      res.send('fail')
      return
    }
    const encrypt = req.body.Encrypt
    const wxmsg = new WXMsgCrypto(callbackToken, callbackAesKey, appID)
    const checkSign = wxmsg.getSignature(timestamp, nonce, encrypt)
    if (checkSign !== msgSignature) {
      console.log('check msg sign fail')
      res.send('fail')
      return
    }
    const result = wxmsg.decrypt(encrypt)
    const appId = result.appId
    const message = JSON.parse(result.message)
    console.log('result', { appId, message })
    if (message.MsgType !== 'event') {
      res.send('success')
      return
    }
    const userOpenId = message.FromUserName
    const event = message.Event
    const createTime = message.CreateTime
    const timeStr = dayjs(createTime * 1000).format('YYYY-MM-DD HH:mm:ss')
    if (event === 'subscribe_msg_popup_event' || event === 'subscribe_msg_change_event') {
      // subscribe_msg_popup_event 当用户触发订阅消息弹框后
      // subscribe_msg_change_event 当用户在手机端服务通知里消息卡片右上角“...”管理消息时 目前只推送取消订阅的事件，即对消息设置“拒收”
      const list = message.List instanceof Array ? message.List : [message.List]
      for (let i = 0; i < list.length; i++) {
        const item = list[i]
        // console.log(item)
        const templateId = item.TemplateId
        if (item.SubscribeStatusString === 'accept') {
          await kn('user_subscribe_templates')
            .insert({ userOpenId, templateId, status: 'reject', updateAt: timeStr, times: 0 })
            .onConflict().merge({
              status: 'reject',
              updateAt: timeStr,
              times: kn.raw('times + 1')
            })
        } else if (item.SubscribeStatusString === 'reject') {
          await kn('user_subscribe_templates').insert({ userOpenId, templateId, status: 'reject', updateAt: timeStr, times: 0 }).onConflict().merge(['status', 'times', 'updateAt'])
        }
      }
    } else if (event === 'subscribe_msg_send_event') {
      // 调用订阅消息接口发送消息给用户的最终结果
    }
    res.send('success')
  } catch (error) {
    console.log('wechat callback fail')
    console.error(error)
    res.send('fail')
  }
})
export default router
