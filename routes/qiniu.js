import { Router } from 'express'
import kn from '../db/kn.js'
import { isQiniuCallback } from '../helper/qiniu.js'
const router = new Router()

router.post('/callback', (req, res, next) => {
  res.status(200).end()
  const isQn = isQiniuCallback(req)
  if (isQn) {
    // 插入记录表
    const { user, mimeType, key, fsize, imageAve, imageInfo } = req.body
    kn('user_media')
      .insert({ path: key, prefix: key.split('/')[0], userId: user, type: mimeType.split('/')[0], fsize, imageAve: JSON.stringify(imageAve), imageInfo: JSON.stringify(imageInfo) })
      .asCallback(function (err) { if (err) console.error(err) })
  }
})

router.post('/audit/callback', (req, res, next) => {
  res.status(200).end()
  const isQn = isQiniuCallback(req)
  if (!isQn) return
  if (req.body.code !== 0) return
  if (!req.body.items || !req.body.items[0] || req.body.items[0].code !== 0) return
  const { inputKey, items: [{ result: { disable, result: { scenes, suggestion } } }] } = req.body
  console.log(inputKey, disable, JSON.stringify(scenes), suggestion)
  auditCallback(inputKey, disable, scenes, suggestion).catch(err => console.error(err))
})

async function auditCallback (inputKey, disable, scenes, suggestion) {
  const item = await kn('user_media').where({ path: inputKey }).first()
  if (!item) return
  await kn('user_media').where({ id: item.id }).update({ disable, scenes: JSON.stringify(scenes), suggestion })
  if (!disable) return
  const prefix = inputKey.split('/')[0]
  if (prefix === 'status') {
    await kn('status_image').where({ userId: item.userId, path: inputKey }).update({ illegal: true })
  } else if (prefix === 'avatar') {
    await kn('user').where({ id: item.userId }).update({ avatar: '' })
  }
}

export default router
