import { Router } from 'express'
import MPApi from '../helper/mp-api.js'
const router = Router()

router.get('/barcode', async (req, res) => {
  const { app, page, scene } = req.query
  if (!app || !page || !scene) {
    res.status(400).send('')
    return
  }
  if (!['tqq', 'shen'].includes(app)) {
    res.status(400).send('')
    return
  }
  const mp = new MPApi(app)
  const data = await mp.getMiniBarCodeUnlimited(page, scene)
  res.contentType('image/jpeg')
  res.end(data, 'binary')
})

export default router
