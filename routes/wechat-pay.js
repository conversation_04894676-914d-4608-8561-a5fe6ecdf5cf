import { Router } from 'express'
import dayjs from 'dayjs'
import objectSupport from 'dayjs/plugin/objectSupport.js'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore.js'
import { verifySignature, decryptResource } from '../helper/wechat-pay.js'
import kn from '../db/kn.js'

dayjs.extend(objectSupport)
dayjs.extend(isSameOrBefore)
const router = Router()

async function verify (req, res, next) {
  try {
    const signature = req.get('Wechatpay-Signature')
    const timestamp = req.get('Wechatpay-Timestamp')
    const nonce = req.get('Wechatpay-Nonce')
    const serial = req.get('Wechatpay-Serial')
    const body = req.body
    if (!signature || !timestamp || !nonce || !serial) {
      console.log(req.headers)
      res.status(500).json({ code: 'FAIL', message: '缺少参数' })
      return
    }
    const isWechatCallback = await verifySignature(serial, signature, timestamp, nonce, body)
    if (!isWechatCallback) {
      console.log('wechat callback verify fail')
      console.log(req.headers)
      console.log(req.body)
      res.status(500).json({ code: 'FAIL', message: '验证失败' })
      return
    }
    next()
  } catch (error) {
    res.status(500).json({ code: 'FAIL', message: '失败' })
  }
}
router.post('/pay', verify, async (req, res) => {
  try {
    console.log('pay callback')
    const body = req.body
    console.log(body)
    if (body.event_type === 'TRANSACTION.SUCCESS') {
      const info = decryptResource(body.resource)
      console.log(info)
      const order = await kn('order').where({ outTradeNo: info.out_trade_no }).first()
      if (!order) {
        console.error('Order not found:', info.out_trade_no)
        res.status(200).end()
        return
      }
      if (info.trade_state === 'SUCCESS') {
        const orderId = order.id
        if (order.stauts === 0) {
          await kn.transaction(async (trx) => {
            if (order.courseId) {
              await trx('user_course').insert({ userId: order.userId, courseId: order.courseId, orderId })
            } else if (order.packageId) {
              // console.log('package id')
            } else {
              const orderProducts = await trx('order_product').where({ orderId }).select()
              const products = await trx('product').whereIn('id', orderProducts.map((product) => product.productId))
              const planProducts = products.filter((product) => product.isPlan)
              // ++++沈老师教育服务
              const shenProducts = products.filter(p => p.isShenService)
              for (const planProduct of planProducts) {
                const plans = Array(planProduct.quantity || 1).fill({ userId: order.userId, orderId, status: 0, app: order.app, isShenPaybackPlan: planProduct.isShenPaybackPlan, dates: planProduct.planDates })
                await trx('plan').insert(plans)
              }
              if (shenProducts.length) {
                const today = dayjs({ hour: 0 })
                const user = await trx('user').where({ id: order.userId }).first()
                const referrerUserId = user.referrerUserId ?? 0
                // 推荐人是否有返利
                const referrerUserHasCommission = false
                // if (referrerUserId) {
                //   const todayStr = today.format('YYYY-MM-DD')
                //   const referrerUserService = await trx('shen_service').where({ id: user.referrerUserId }).where('start', '<=', todayStr).where('end', '>=', todayStr).first()
                //   if (referrerUserService) referrerUserHasCommission = true
                // }
                for (const product of shenProducts) {
                  // 判断是否有正在服务中的
                  const currentItem = await trx('shen_service').where({ userId: order.userId })
                    .where('start', '<=', today.format('YYYY-MM-DD'))
                    .where('end', '>=', today.format('YYYY-MM-DD')).first()
                  const start = currentItem ? dayjs(currentItem.end).add(1, 'days') : today.clone()
                  // isShenService： 1：1 年，2：半年
                  const end = product.isShenService === 1 ? start.clone().add(364, 'days') : start.clone().add(6, 'months')
                  const [shenServiceId] = await trx('shen_service').insert({
                    orderId,
                    userId: order.userId,
                    referrerUserId,
                    referrerUserHasCommission,
                    paybackEverydayAmount: product.shenServicePaybackEverydayAmount || 618,
                    start: start.format('YYYY-MM-DD'),
                    end: end.format('YYYY-MM-DD')
                  })
                  const dates = []
                  let current = start.clone()
                  while (current.isSameOrBefore(end)) {
                    dates.push({
                      shenServiceId,
                      date: current.format('YYYY-MM-DD')
                    })
                    current = current.add(1, 'day')
                  }
                  await trx('shen_service_day').insert(dates)
                }
              }
            }
            await trx('order').where({ id: orderId }).update({
              status: order.needShipping ? 1 : 10,
              payStatus: 1,
              payTransactionId: info.transaction_id,
              paySuccessTime: dayjs(info.success_time).format('YYYY-MM-DD HH:mm:ss')
            })
          })
        }
      }
    }
    res.status(200).end()
  } catch (error) {
    console.error(error)
    res.status(500)
    res.json({ code: 'FAIL', message: '失败' })
  }
})

router.post('/refund', verify, async (req, res) => {
  try {
    console.log('refund callback')
    const body = req.body
    console.log(body)
    if (body?.resource?.original_type === 'refund') {
      const info = decryptResource(body.resource)
      console.log(info)
      const refund = await kn('order_refund').where({ refundNo: info.out_refund_no }).first()
      if (!refund) {
        console.error('Refund not found:', info.out_refund_no)
        res.status(200).end()
        return
      }
      // const status = info.refund_status
      await kn('order_refund').where({ id: refund.id }).update({ status: info.refund_status, updateAt: kn.raw('CURRENT_TIMESTAMP') })
      // const orderNo = info.out_trade_no.slice(orderPrefix.length)
      //   const orderId = parseInt(orderNo)
      // if (info.out_refund_no.indexOf(orderPrefix) === 0) {
      //   const refundNo = info.out_refund_no.slice(orderPrefix.length)
      //   const status = info.refund_status
      //   const refund = await kn('order_refund').where({ refundNo }).first()
      //   if (refund) {
      //     await kn('order_refund').where({ id: refund.id }).update({ status, updateAt: kn.raw('CURRENT_TIMESTAMP') })
      //   } else {
      //     console.error('Refund not found:', refundNo)
      //   }
      // }
      //  if (info.refund_status === 'ABNORMAL') {
      //   // todo 发送通知
      // }
    }
    res.status(200).end()
  } catch (error) {
    console.error('Error in refund callback:', error)
    res.status(500).json({ code: 'FAIL', message: '处理退款回调时发生错误' })
  }
})

router.post('/transfer', verify, async (req, res) => {
  try {
    console.log('transfer callback')
    const body = req.body
    console.log(body)
    if (body.resource.original_type === 'mch_payment') {
      const info = decryptResource(body.resource)
      console.log(info)
      const outBatchNo = info.out_batch_no
      const transfer = await kn('wechat_pay_transfer').where({ outBatchNo }).first()
      if (!transfer) {
        console.error('Transfer not found:', outBatchNo)
        res.status(200).end()
        return
      }
      const updates = { batchStatus: info.batch_status }
      if (info.success_num !== undefined) updates.successNum = info.success_num
      if (info.success_amount !== undefined) updates.successAmount = info.success_amount
      if (info.fail_num !== undefined) updates.failNum = info.fail_num
      if (info.fail_amount !== undefined) updates.failAmount = info.fail_amount
      if (info.update_time !== undefined) updates.updateTime = dayjs(info.update_time).format('YYYY-MM-DD HH:mm:ss')
      await kn('wechat_pay_transfer').where({ id: transfer.id }).update(updates)
      // order_commission
      await kn('order_commission').where({ transferId: transfer.id }).update({ status: updates.batchStatus })
    }
    res.status(200).end()
  } catch (error) {
    console.error(error)
    res.status(500).json({ code: 'FAIL', message: '处理转账回调时发生错误' })
  }
})

export default router
